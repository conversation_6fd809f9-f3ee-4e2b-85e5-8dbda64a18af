# MPPET-RLE: Multi-Person Pose Estimation and Tracking with Residual Log-likelihood Estimation

이 리포지토리는 PoseTrack21 데이터셋을 사용한 다중 인물 자세 추정 및 추적을 위한 RLE(Residual Log-likelihood Estimation) 기반 모델을 구현합니다.

## 프로젝트 구조

```
pktrack/
├── models/                          # 모델 아키텍처
│   ├── __init__.py
│   ├── mppet_rle.py                # 메인 MPPET-RLE 모델
│   ├── pose_estimator.py           # RLE 기반 자세 추정기
│   └── losses.py                   # RLE 손실 함수들
├── detection/                       # 인물 탐지 모듈
│   ├── __init__.py
│   └── detector.py                 # Faster R-CNN 기반 탐지기
├── tracking/                        # 추적 모듈
│   ├── __init__.py
│   ├── tracker.py                  # 메인 추적기
│   ├── association.py              # 데이터 연관
│   ├── motion_model.py             # 동작 모델
│   └── track_manager.py            # 트랙 관리
├── datasets/                        # 데이터셋 처리
│   ├── __init__.py
│   ├── posetrack_dataset.py        # PoseTrack21 데이터로더
│   └── PoseTrack21/                # 데이터셋 파일들
├── layers/                          # 네트워크 레이어
│   ├── __init__.py
│   ├── Resnet.py                   # ResNet 백본
│   └── real_nvp.py                 # Normalizing Flow 레이어
├── utils/                           # 유틸리티 함수들
│   ├── __init__.py
│   ├── metrics.py                  # 평가 메트릭
│   ├── posetrack21_metrics.py      # PoseTrack21 전용 메트릭
│   └── visualization.py           # 시각화 도구
├── configs/                         # 설정 파일들
│   ├── __init__.py
│   ├── base_config.py              # 기본 설정
│   └── posetrack21_config.py       # PoseTrack21 전용 설정
├── train_posetrack21.py            # 학습 스크립트
├── test_posetrack21.py             # 테스트 스크립트
├── builder.py                       # 레지스트리 빌더
├── requirements.txt                 # 의존성 패키지
└── README.md                       # 프로젝트 설명
```

## 주요 특징

1. **RLE 기반 불확실성 추정**: 자세 키포인트의 위치뿐만 아니라 불확실성도 함께 예측
2. **다중 인물 처리**: Faster R-CNN을 통한 인물 탐지 후 개별 자세 추정
3. **분포 인식 추적**: 불확실성 정보를 활용한 강건한 다중 인물 추적
4. **PoseTrack21 최적화**: PoseTrack21 데이터셋에 특화된 구현

## 설치 방법

```bash
pip install -r requirements.txt
```

## 사용 방법

### 학습
```bash
python train_posetrack21.py --config configs/posetrack21_config.py
```

### 테스트
```bash
python test_posetrack21.py --checkpoint checkpoints_posetrack21/best_model.pth
```

## 모델 아키텍처

### MPPET-RLE 모델
- **탐지 단계**: Faster R-CNN 기반 인물 탐지
- **자세 추정 단계**: RLE 기반 단일 인물 자세 추정
- **추적 단계**: 불확실성 정보를 활용한 데이터 연관 및 추적

### RLE 손실 함수
- 기본 RLE 손실: L1 손실 + 음의 로그 가능도
- 적응형 RLE 손실: 학습 가능한 가중치가 포함된 손실
- 다중 인물 RLE 손실: 여러 인물에 대한 평균 손실

## 데이터셋

PoseTrack21 데이터셋을 사용하며, 다음과 같은 구조를 가집니다:
- 17개 키포인트 (COCO 형식)
- 비디오 시퀀스 기반 다중 인물 데이터
- 학습/검증 분할

## 평가 메트릭

- PCK (Percentage of Correct Keypoints)
- PCKh (Head-normalized PCK)
- MOTA/MOTP (Multi-Object Tracking Accuracy/Precision)
- IDF1 (Identity F1 Score)

## 참고 논문

- RLE: Residual Log-likelihood Estimation for Regression
- PoseTrack21: A Dataset for Person Search, Multi-Object Tracking and Multi-Person Pose Tracking
