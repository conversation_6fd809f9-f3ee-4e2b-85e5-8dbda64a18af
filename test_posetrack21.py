"""
Test script for MPPET-RLE with PoseTrack21 data
"""

import os
os.environ['QT_QPA_PLATFORM'] = 'offscreen'
import torch
import torch.nn as nn
import numpy as np
import argparse
from tqdm import tqdm
import logging
import json
from datetime import datetime

from models import MPPET_RLE, build_mppet_rle
from datasets.posetrack_dataset import PoseTrackDataset, get_transform, collate_fn
from utils.posetrack21_metrics import PoseTrack21Evaluator
from configs.posetrack21_config import posetrack21_config
import cv2


def setup_logging(log_dir):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'posetrack21_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


def test_model(model, dataloader, device, logger, save_predictions=True):
    """Test the model on validation/test data."""
    model.eval()
    
    all_predictions = []
    all_targets = []
    
    evaluator = PoseTrack21Evaluator()
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc='Testing')
        
        for batch_idx, batch_data in enumerate(pbar):
            try:
                # Extract data
                images = batch_data['images']
                poses = batch_data['poses']
                pose_weights = batch_data['pose_weights']
                image_info = batch_data.get('image_info', [])
                
                if len(images) == 0:
                    continue
                
                # Move to device
                images = images.to(device)
                
                # Set model to detection mode for testing
                model.set_mode('detect')
                
                # Get predictions
                predictions = model(images, mode='detect')
                
                # Process predictions for evaluation
                for i, pred_list in enumerate(predictions):
                    if len(pred_list) > 0:
                        pred = pred_list[0]  # Take first detection
                        
                        # Convert to evaluation format
                        pred_dict = {
                            'pose': pred['pose_mu'],
                            'pose_sigma': pred['pose_sigma'],
                            'bbox': pred['bbox'],
                            'score': pred['score']
                        }
                        
                        if i < len(image_info):
                            pred_dict['image_info'] = image_info[i]
                        
                        all_predictions.append(pred_dict)
                
                # Process targets
                if poses is not None and len(poses) > 0:
                    for i in range(len(poses)):
                        if i < len(poses):
                            target_dict = {
                                'pose': poses[i].cpu().numpy(),
                                'pose_weight': pose_weights[i].cpu().numpy() if pose_weights is not None else None
                            }
                            
                            if i < len(image_info):
                                target_dict['image_info'] = image_info[i]
                            
                            all_targets.append(target_dict)
                
                # Update progress
                pbar.set_postfix({
                    'Batch': f'{batch_idx+1}/{len(dataloader)}',
                    'Predictions': len(all_predictions)
                })
                
            except Exception as e:
                logger.error(f"Error in batch {batch_idx}: {str(e)}")
                continue
    
    # Evaluate predictions
    if len(all_predictions) > 0 and len(all_targets) > 0:
        logger.info(f"Evaluating {len(all_predictions)} predictions against {len(all_targets)} targets")
        
        # Compute metrics
        metrics = evaluator.evaluate(all_predictions, all_targets)
        
        # Log results
        logger.info("Test Results:")
        for metric_name, metric_value in metrics.items():
            logger.info(f"  {metric_name}: {metric_value:.4f}")
        
        # Save predictions if requested
        if save_predictions:
            predictions_file = os.path.join(
                posetrack21_config.paths.log_dir,
                f'test_predictions_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
            )
            
            # Convert numpy arrays to lists for JSON serialization
            serializable_predictions = []
            for pred in all_predictions:
                serializable_pred = {}
                for key, value in pred.items():
                    if isinstance(value, np.ndarray):
                        serializable_pred[key] = value.tolist()
                    else:
                        serializable_pred[key] = value
                serializable_predictions.append(serializable_pred)
            
            with open(predictions_file, 'w') as f:
                json.dump({
                    'predictions': serializable_predictions,
                    'metrics': metrics,
                    'config': dict(posetrack21_config)
                }, f, indent=2)
            
            logger.info(f"Predictions saved to {predictions_file}")
        
        return metrics
    else:
        logger.warning("No valid predictions or targets found for evaluation")
        return {}


def main():
    parser = argparse.ArgumentParser(description='Test MPPET-RLE on PoseTrack21')
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to model checkpoint')
    parser.add_argument('--data_root', type=str, 
                       default='/home/<USER>/workspace/pktrack/datasets/PoseTrack21',
                       help='Path to PoseTrack21 dataset')
    parser.add_argument('--split', type=str, default='val',
                       choices=['val', 'test'],
                       help='Dataset split to test on')
    parser.add_argument('--batch_size', type=int, default=4,
                       help='Batch size for testing')
    parser.add_argument('--device', type=str, default='cuda',
                       help='Device to use for testing')
    parser.add_argument('--save_predictions', action='store_true',
                       help='Save predictions to file')
    parser.add_argument('--log_dir', type=str, default='logs_posetrack21',
                       help='Directory for logs')
    
    args = parser.parse_args()
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info(f"Starting PoseTrack21 testing")
    logger.info(f"Arguments: {args}")
    
    # Set device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Create model
    logger.info("Creating model...")
    model = build_mppet_rle(posetrack21_config)
    model = model.to(device)
    
    # Load checkpoint
    logger.info(f"Loading checkpoint from {args.checkpoint}")
    checkpoint = torch.load(args.checkpoint, map_location=device)
    
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
        logger.info(f"Loaded checkpoint from epoch {checkpoint.get('epoch', 'unknown')}")
    else:
        model.load_state_dict(checkpoint)
        logger.info("Loaded checkpoint (legacy format)")
    
    # Create dataset and dataloader
    logger.info(f"Creating {args.split} dataset...")
    
    # Use simple transforms for testing
    test_transform = get_transform(train=False)
    
    test_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split=args.split,
        transform=test_transform,
        sequence_length=posetrack21_config.dataset.sequence_length,
        input_size=posetrack21_config.pose.input_size,
        normalize_coords=posetrack21_config.dataset.normalize_coords,
        use_gt_bbox=posetrack21_config.dataset.use_gt_bbox
    )
    
    test_dataloader = torch.utils.data.DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        shuffle=False,
        num_workers=posetrack21_config.data.num_workers,
        pin_memory=posetrack21_config.data.pin_memory,
        collate_fn=collate_fn
    )
    
    logger.info(f"Created dataset with {len(test_dataset)} samples")
    logger.info(f"Test dataloader has {len(test_dataloader)} batches")
    
    # Test model
    logger.info("Starting testing...")
    metrics = test_model(
        model=model,
        dataloader=test_dataloader,
        device=device,
        logger=logger,
        save_predictions=args.save_predictions
    )
    
    logger.info("Testing completed!")
    logger.info("Final Results:")
    for metric_name, metric_value in metrics.items():
        logger.info(f"  {metric_name}: {metric_value:.4f}")


if __name__ == '__main__':
    main()
