import os
import json
import torch
import numpy as np
from torch.utils.data import Dataset
import cv2
from PIL import Image
import torchvision.transforms as transforms
import glob
from collections import defaultdict


class PoseTrackDataset(Dataset):
    """
    PoseTrack21 dataset for multi-person pose estimation and tracking

    Expected directory structure:
    data_root/
    ├── images/
    │   ├── train/
    │   │   ├── video_001/
    │   │   │   ├── 000001.jpg
    │   │   │   └── ...
    │   │   └── ...
    │   └── val/
    └── annotations/
        ├── train/
        │   ├── video_001.json
        │   └── ...
        └── val/
    """
    def __init__(self, data_root, split='train', transform=None, sequence_length=2,
                 input_size=(192, 256), normalize_coords=True, use_gt_bbox=True):
        self.data_root = data_root
        self.split = split
        self.transform = transform
        self.sequence_length = sequence_length
        self.input_size = input_size  # (width, height)
        self.normalize_coords = normalize_coords
        self.use_gt_bbox = use_gt_bbox

        # PoseTrack21 keypoint names (17 joints)
        self.keypoint_names = [
            "nose", "head_bottom", "head_top", "left_ear", "right_ear",
            "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
            "left_wrist", "right_wrist", "left_hip", "right_hip",
            "left_knee", "right_knee", "left_ankle", "right_ankle"
        ]
        self.num_joints = len(self.keypoint_names)

        # Image normalization stats (ImageNet)
        self.image_mean = np.array([0.485, 0.456, 0.406])
        self.image_std = np.array([0.229, 0.224, 0.225])

        # Load and validate dataset
        self.annotations = self._load_annotations()
        self.sequences = self._organize_sequences()
        self.samples = self._create_samples()

        print(f"Loaded {len(self.samples)} samples from {split} split")
        print(f"Input size: {self.input_size}, Normalize coords: {self.normalize_coords}")
    
    def _load_annotations(self):
        """Load PoseTrack21 annotations with proper error handling"""
        # Try real PoseTrack21 data first
        posetrack_dir = os.path.join(self.data_root, 'data', 'posetrack_data', self.split)

        if os.path.exists(posetrack_dir):
            print(f"Loading real PoseTrack21 data from {posetrack_dir}")
            return self._load_real_posetrack_data(posetrack_dir)

        # Fallback to old annotation structure
        annotation_dir = os.path.join(self.data_root, 'annotations', self.split)

        if os.path.exists(annotation_dir):
            print(f"Loading annotations from {annotation_dir}")
            annotations = {}
            json_files = glob.glob(os.path.join(annotation_dir, '*.json'))

            for json_path in json_files:
                try:
                    with open(json_path, 'r') as f:
                        data = json.load(f)

                    # Extract video ID from filename
                    vid_id = os.path.splitext(os.path.basename(json_path))[0]
                    annotations[vid_id] = data

                except Exception as e:
                    print(f"Error loading {json_path}: {e}")
                    continue

            if annotations:
                return annotations

        # Create dummy data as last resort
        print("No real data found, creating dummy data for testing...")
        return self._create_dummy_annotations()

    def _load_real_posetrack_data(self, posetrack_dir):
        """Load real PoseTrack21 data"""
        annotations = {}
        json_files = glob.glob(os.path.join(posetrack_dir, '*.json'))

        print(f"Found {len(json_files)} PoseTrack21 annotation files")

        for json_path in json_files:
            try:
                with open(json_path, 'r') as f:
                    data = json.load(f)

                # Extract video ID from filename (e.g., "000001_bonn_train.json" -> "000001_bonn_train")
                vid_id = os.path.splitext(os.path.basename(json_path))[0]

                # Filter only labeled frames that actually have person annotations
                labeled_images = []
                annotations_list = data.get('annotations', [])

                # Create mapping of image_id to person annotations
                image_to_persons = {}
                for ann in annotations_list:
                    if ann.get('category_id') == 1:  # person category
                        img_id = ann.get('image_id')
                        if img_id not in image_to_persons:
                            image_to_persons[img_id] = []
                        image_to_persons[img_id].append(ann)

                for img in data.get('images', []):
                    img_id = img.get('id')
                    # Only include images that have person annotations
                    if img_id in image_to_persons and len(image_to_persons[img_id]) > 0:
                        labeled_images.append(img)

                if labeled_images:
                    # Update data with only labeled images
                    data['images'] = labeled_images
                    annotations[vid_id] = data
                    print(f"Loaded {vid_id}: {len(labeled_images)} labeled frames")

            except Exception as e:
                print(f"Error loading {json_path}: {e}")
                continue

        print(f"Successfully loaded {len(annotations)} video sequences")
        return annotations

    def _create_dummy_annotations(self):
        """Create dummy annotations for testing when real data is not available"""
        dummy_annotations = {}

        # Create a few dummy video sequences
        for vid_idx in range(3):
            vid_id = f"dummy_video_{vid_idx:03d}"

            images = []
            annotations_list = []

            # Create dummy frames
            for frame_idx in range(10):
                image_id = frame_idx
                images.append({
                    'id': image_id,
                    'file_name': f"dummy/{self.split}/{vid_id}/{frame_idx:06d}.jpg",  # Use dummy prefix
                    'width': 640,
                    'height': 480,
                    'has_labeled_person': True
                })

                # Create dummy person annotations
                for person_idx in range(2):  # 2 persons per frame
                    # Random bounding box
                    x = float(np.random.randint(50, 400))
                    y = float(np.random.randint(50, 300))
                    w = float(np.random.randint(80, 150))
                    h = float(np.random.randint(120, 200))

                    # Random keypoints within bbox
                    keypoints = []
                    for joint_idx in range(17):
                        kx = float(x + np.random.randint(0, int(w)))
                        ky = float(y + np.random.randint(0, int(h)))
                        visibility = 2  # Always visible for dummy data
                        keypoints.extend([kx, ky, visibility])

                    annotation = {
                        'id': len(annotations_list),
                        'image_id': image_id,
                        'category_id': 1,  # person
                        'bbox': [x, y, w, h],
                        'keypoints': keypoints,
                        'track_id': person_idx,
                        'area': float(w * h),
                        'iscrowd': 0
                    }
                    annotations_list.append(annotation)

            dummy_annotations[vid_id] = {
                'images': images,
                'annotations': annotations_list,
                'categories': [{'id': 1, 'name': 'person'}]
            }

        print(f"Created {len(dummy_annotations)} dummy video sequences")
        return dummy_annotations
    
    def _organize_sequences(self):
        """Organize annotations by video sequences with validation"""
        sequences = {}

        for vid_id, annotation in self.annotations.items():
            try:
                images = annotation.get('images', [])
                annotations_list = annotation.get('annotations', [])

                if not images:
                    print(f"Warning: No images found for video {vid_id}")
                    continue

                # Create image_id to annotation mapping
                image_annotations = defaultdict(list)
                for ann in annotations_list:
                    if 'image_id' in ann and 'category_id' in ann:
                        # Only keep person annotations (category_id = 1)
                        if ann['category_id'] == 1:
                            image_id = ann['image_id']
                            image_annotations[image_id].append(ann)

                # Sort images by frame ID
                images.sort(key=lambda x: x.get('id', 0))

                # Filter images that have person annotations
                valid_images = []
                for img in images:
                    img_id = img.get('id')
                    if img_id in image_annotations and len(image_annotations[img_id]) > 0:
                        valid_images.append(img)

                # Debug info for first few videos
                if len(sequences) < 3:
                    print(f"Debug {vid_id}: {len(images)} total images, {len(image_annotations)} with annotations, {len(valid_images)} valid")
                    if len(valid_images) > 0:
                        print(f"  First valid image: {valid_images[0].get('file_name', 'unknown')}")
                        print(f"  Annotations for first image: {len(image_annotations[valid_images[0]['id']])}")

                if len(valid_images) >= self.sequence_length:
                    sequences[vid_id] = {
                        'images': valid_images,
                        'annotations': dict(image_annotations)
                    }
                else:
                    if len(sequences) < 3:  # Only show warning for first few
                        print(f"Warning: Video {vid_id} has only {len(valid_images)} valid frames, need at least {self.sequence_length}")

            except Exception as e:
                print(f"Error organizing sequence {vid_id}: {e}")
                continue

        print(f"Organized {len(sequences)} valid video sequences")
        return sequences
    
    def _create_samples(self):
        """Create training samples with proper validation"""
        samples = []

        for vid_id, sequence in self.sequences.items():
            images = sequence['images']
            annotations = sequence['annotations']

            # Create consecutive frame sequences
            for i in range(len(images) - self.sequence_length + 1):
                frame_sequence = []
                valid_sequence = True

                for j in range(self.sequence_length):
                    frame_idx = i + j
                    if frame_idx >= len(images):
                        valid_sequence = False
                        break

                    image_info = images[frame_idx]
                    image_id = image_info.get('id')

                    # Check if frame has valid annotations
                    if image_id not in annotations or len(annotations[image_id]) == 0:
                        valid_sequence = False
                        break

                    # Validate that image file exists (for real data)
                    file_name = image_info.get('file_name', '')
                    if file_name.startswith('images/'):
                        # Real PoseTrack21 data - try different path combinations
                        possible_paths = [
                            os.path.join(self.data_root, file_name),  # Direct path
                            os.path.join(self.data_root, 'data', file_name),  # With data/ prefix
                        ]

                        image_path = None
                        for path in possible_paths:
                            if os.path.exists(path):
                                image_path = path
                                break

                        if image_path is None:
                            if len(samples) < 3:  # Debug first few
                                print(f"  Image not found. Tried paths:")
                                for path in possible_paths:
                                    print(f"    {path} (exists: {os.path.exists(path)})")
                            valid_sequence = False
                            break
                    else:
                        # For dummy data, we don't check file existence
                        image_path = None

                    frame_sequence.append({
                        'vid_id': vid_id,
                        'image_info': image_info,
                        'annotations': annotations[image_id],
                        'image_path': image_path
                    })

                if valid_sequence and len(frame_sequence) == self.sequence_length:
                    samples.append(frame_sequence)

        print(f"Created {len(samples)} valid training samples")
        return samples
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sequence = self.samples[idx]

        # Load images and annotations for the sequence
        images = []
        all_annotations = []
        frame_info = []

        for frame_data in sequence:
            # Load image
            if frame_data['image_path'] and os.path.exists(frame_data['image_path']):
                # Real image
                image = cv2.imread(frame_data['image_path'])
                if image is None:
                    # Create dummy image if loading fails
                    image = self._create_dummy_image()
                else:
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            else:
                # Create dummy image for testing
                image = self._create_dummy_image()

            images.append(image)
            all_annotations.append(frame_data['annotations'])
            frame_info.append(frame_data['image_info'])

        # Process for multi-person tracking
        return self._process_sequence(images, all_annotations, frame_info)

    def _create_dummy_image(self):
        """Create a dummy image for testing"""
        # Create a simple gradient image
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        for i in range(480):
            for j in range(640):
                image[i, j] = [i % 256, j % 256, (i + j) % 256]
        return image
    
    def _process_sequence(self, images, all_annotations, frame_info):
        """Process sequence for multi-person pose tracking with proper normalization"""
        # Focus on the first frame for pose estimation
        current_image = images[0]
        current_annotations = all_annotations[0]
        current_frame_info = frame_info[0]

        # Get next frame for temporal supervision if available
        if len(images) > 1:
            next_image = images[1]
            next_annotations = all_annotations[1]
            next_frame_info = frame_info[1]
        else:
            next_image = current_image
            next_annotations = current_annotations
            next_frame_info = current_frame_info

        # Extract person crops and poses from current frame
        person_data = self._extract_person_data(current_image, current_annotations, current_frame_info)

        # Extract temporal targets if next frame is available
        temporal_targets = self._extract_temporal_targets(
            current_annotations, next_annotations, current_image.shape[:2]
        )

        # Apply transforms to person crops
        processed_person_data = []
        for crop, pose, pose_weight, bbox, person_id in person_data:
            # Apply image transforms
            if self.transform:
                if isinstance(crop, np.ndarray):
                    crop_pil = Image.fromarray(crop.astype(np.uint8))
                    crop_transformed = self.transform(crop_pil)
                else:
                    crop_transformed = self.transform(crop)
            else:
                # Default normalization
                crop_transformed = torch.from_numpy(crop.astype(np.float32)).permute(2, 0, 1) / 255.0
                # Apply ImageNet normalization
                for c in range(3):
                    crop_transformed[c] = (crop_transformed[c] - self.image_mean[c]) / self.image_std[c]

            processed_person_data.append({
                'image': crop_transformed,
                'pose': torch.from_numpy(pose.astype(np.float32)),
                'pose_weight': torch.from_numpy(pose_weight.astype(np.float32)),
                'bbox': torch.from_numpy(np.array(bbox, dtype=np.float32)),
                'person_id': person_id
            })

        return {
            'person_data': processed_person_data,
            'temporal_targets': temporal_targets,
            'frame_info': {
                'vid_id': frame_info[0].get('vid_id', ''),
                'frame_id': current_frame_info.get('id', 0),
                'image_size': (current_frame_info.get('width', 640), current_frame_info.get('height', 480))
            }
        }
    
    def _extract_person_data(self, image, annotations, frame_info):
        """Extract person crops and pose data with proper normalization"""
        person_data = []
        img_h, img_w = image.shape[:2]

        for ann_idx, ann in enumerate(annotations):
            if ann.get('category_id', 1) != 1:  # Only person category
                continue

            try:
                # Get bounding box [x, y, width, height]
                bbox = ann.get('bbox', [0, 0, 100, 100])
                x, y, w, h = bbox
                x1, y1, x2, y2 = int(x), int(y), int(x + w), int(y + h)

                # Add padding and ensure bounds
                padding = max(10, int(min(w, h) * 0.1))
                x1 = max(0, x1 - padding)
                y1 = max(0, y1 - padding)
                x2 = min(img_w, x2 + padding)
                y2 = min(img_h, y2 + padding)

                # Skip if bbox is too small
                if (x2 - x1) < 20 or (y2 - y1) < 20:
                    continue

                # Extract and resize crop
                crop = image[y1:y2, x1:x2]
                if crop.size == 0:
                    continue

                crop_resized = cv2.resize(crop, self.input_size)

                # Get keypoints [x, y, visibility] * 17
                keypoints = np.array(ann.get('keypoints', [0] * 51)).reshape(-1, 3)
                if len(keypoints) != 17:
                    # Pad or truncate to 17 joints
                    if len(keypoints) < 17:
                        padding_needed = 17 - len(keypoints)
                        keypoints = np.vstack([keypoints, np.zeros((padding_needed, 3))])
                    else:
                        keypoints = keypoints[:17]

                # Convert keypoints to crop coordinates
                pose_in_crop = keypoints.copy().astype(np.float32)
                scale_x = float(self.input_size[0]) / float(x2 - x1)
                scale_y = float(self.input_size[1]) / float(y2 - y1)

                pose_in_crop[:, 0] = (pose_in_crop[:, 0] - float(x1)) * scale_x
                pose_in_crop[:, 1] = (pose_in_crop[:, 1] - float(y1)) * scale_y

                # Normalize coordinates if requested
                if self.normalize_coords:
                    pose_in_crop[:, 0] /= float(self.input_size[0])  # Normalize to [0, 1]
                    pose_in_crop[:, 1] /= float(self.input_size[1])

                # Extract pose coordinates and weights
                pose_coords = pose_in_crop[:, :2]  # (17, 2)
                pose_weights = pose_in_crop[:, 2:3]  # (17, 1) - visibility as weight

                # Convert visibility to proper weights
                pose_weights = np.where(pose_weights > 0, 1.0, 0.0)
                pose_weights = np.repeat(pose_weights, 2, axis=1)  # (17, 2) for x,y

                # Get person ID for tracking
                person_id = ann.get('track_id', ann.get('id', ann_idx))

                person_data.append((
                    crop_resized,
                    pose_coords,
                    pose_weights,
                    [x1, y1, x2, y2],
                    person_id
                ))

            except Exception as e:
                print(f"Error processing person annotation: {e}")
                continue

        return person_data
    
    def _extract_temporal_targets(self, current_annotations, next_annotations, image_shape):
        """Extract temporal tracking targets with proper validation"""
        temporal_targets = []

        # Match persons between frames using track_id
        current_tracks = {}
        next_tracks = {}

        for ann in current_annotations:
            track_id = ann.get('track_id', ann.get('id'))
            if track_id is not None:
                current_tracks[track_id] = ann

        for ann in next_annotations:
            track_id = ann.get('track_id', ann.get('id'))
            if track_id is not None:
                next_tracks[track_id] = ann

        for track_id, current_ann in current_tracks.items():
            if track_id in next_tracks:
                next_ann = next_tracks[track_id]

                try:
                    # Get keypoints with explicit float conversion
                    current_keypoints = np.array(current_ann.get('keypoints', [0] * 51), dtype=np.float32).reshape(-1, 3)
                    next_keypoints = np.array(next_ann.get('keypoints', [0] * 51), dtype=np.float32).reshape(-1, 3)

                    # Ensure we have 17 joints
                    if len(current_keypoints) != 17 or len(next_keypoints) != 17:
                        continue

                    # Compute temporal offset (movement between frames) 
                    temporal_offset = next_keypoints[:, :2] - current_keypoints[:, :2]

                    # Normalize offset by image size if needed
                    if self.normalize_coords:
                        temporal_offset = temporal_offset.astype(np.float32)  # 명시적 float 변환
                        temporal_offset[:, 0] /= float(image_shape[1])  # width
                        temporal_offset[:, 1] /= float(image_shape[0])  # height

                    temporal_targets.append({
                        'track_id': track_id,
                        'temporal_offset': torch.from_numpy(temporal_offset.astype(np.float32)),
                        'current_bbox': current_ann.get('bbox', [0, 0, 100, 100]),
                        'next_bbox': next_ann.get('bbox', [0, 0, 100, 100])
                    })

                except Exception as e:
                    print(f"Error extracting temporal target for track {track_id}: {e}")
                    continue

        return temporal_targets


def get_transform(split='train', input_size=(192, 256)):
    """Get data transforms with proper augmentation"""
    if split == 'train':
        transform = transforms.Compose([
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:
        transform = transforms.Compose([
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])

    return transform


def collate_fn(batch):
    """Custom collate function for multi-person pose data"""
    if not batch:
        return {
            'images': torch.empty(0, 3, 256, 192),
            'poses': torch.empty(0, 17, 2),
            'pose_weights': torch.empty(0, 17, 2),
            'bboxes': torch.empty(0, 4),
            'person_ids': [],
            'temporal_targets': [],
            'frame_info': [],
            'image_paths': []
        }

    images = []
    poses = []
    pose_weights = []
    bboxes = []
    person_ids = []
    temporal_targets = []
    frame_info = []
    image_paths = []

    for sample in batch:
        person_data = sample['person_data']
        
        # Extract image paths from first frame of sequence
        sample_image_paths = []
        for frame_data in sample.get('frame_sequence', [sample]):
            if 'image_path' in frame_data:
                sample_image_paths.append(frame_data['image_path'])
            elif hasattr(frame_data, 'get') and frame_data.get('image_path'):
                sample_image_paths.append(frame_data.get('image_path'))
            else:
                # Create dummy path for visualization compatibility
                sample_image_paths.append(f"dummy_image_{len(image_paths)}.jpg")
        
        if not sample_image_paths:
            sample_image_paths = [f"dummy_image_{len(image_paths)}.jpg"]
        
        image_paths.append(sample_image_paths)

        for person in person_data:
            images.append(person['image'])
            poses.append(person['pose'])
            pose_weights.append(person['pose_weight'])
            bboxes.append(person['bbox'])
            person_ids.append(person['person_id'])

        temporal_targets.extend(sample['temporal_targets'])
        frame_info.append(sample['frame_info'])

    # Stack tensors
    result = {
        'images': torch.stack(images) if images else torch.empty(0, 3, 256, 192),
        'poses': torch.stack(poses) if poses else torch.empty(0, 17, 2),
        'pose_weights': torch.stack(pose_weights) if pose_weights else torch.empty(0, 17, 2),
        'bboxes': torch.stack(bboxes) if bboxes else torch.empty(0, 4),
        'person_ids': person_ids,
        'temporal_targets': temporal_targets,
        'frame_info': frame_info,
        'image_paths': image_paths
    }

    return result
