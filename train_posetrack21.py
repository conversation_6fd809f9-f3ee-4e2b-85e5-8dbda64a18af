"""
Training script for MPPET-RLE with real PoseTrack21 data
"""

import os
# Qt 환경 변수 설정 (OpenCV 오류 방지)
os.environ['QT_QPA_PLATFORM'] = 'offscreen'
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
from tqdm import tqdm
import logging
from datetime import datetime

from models import SimpleRegressFlow, MPPET_RLE, build_mppet_rle
from models.losses import RLELoss, AdaptiveRLELoss
from datasets.posetrack_dataset import PoseTrackDataset, get_transform, collate_fn
from utils.posetrack21_metrics import PoseTrack21Evaluator
import cv2

# 시각화 함수 임포트 (오류 처리)
try:
    from utils.visualization import visualize_pose_matching
    VISUALIZATION_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Visualization not available: {e}")
    VISUALIZATION_AVAILABLE = False
    def visualize_pose_matching(*args, **kwargs):
        pass


def setup_logging(log_dir):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f'posetrack21_training_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)


def train_epoch(model, dataloader, optimizer, criterion, device, logger, epoch):
    """Train for one epoch with PoseTrack21 multi-person data."""
    model.train()
    
    total_loss = 0.0
    num_batches = 0
    num_samples = 0
    
    pbar = tqdm(dataloader, desc=f'Epoch {epoch}')
    
    for batch_idx, batch_data in enumerate(pbar):
        try:
            # Clear GPU cache periodically
            if batch_idx % 20 == 0:
                torch.cuda.empty_cache()
                
            # Extract multi-person data
            images = batch_data['images']  # All person crops in the batch
            poses = batch_data['poses']    # All poses in the batch
            pose_weights = batch_data['pose_weights']  # All pose weights
            
            if len(images) == 0:
                continue
                
            # Move to device
            images = images.to(device, non_blocking=True)
            poses = poses.to(device, non_blocking=True) 
            pose_weights = pose_weights.to(device, non_blocking=True)
            
            # Prepare targets for SimpleRegressFlow (each person crop is treated independently)
            targets = {
                'target_uv': poses,
                'target_uv_weight': pose_weights
            }
            
            optimizer.zero_grad()
            
            # Forward pass through pose estimator directly
            # Since we're using person crops, we can train the pose estimator directly
            outputs = model.pose_estimator(images, targets)
            
            # Compute loss using RLE loss
            loss = criterion(outputs, targets)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            
            # Update statistics
            total_loss += loss.item()
            num_batches += 1
            num_samples += len(images)
            
            # Update progress bar
            pbar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Samples': num_samples
            })
            
            # Clear tensors to free memory
            del images, poses, pose_weights, outputs, loss
            
        except torch.cuda.OutOfMemoryError as e:
            logger.error(f"CUDA OOM in training batch {batch_idx}: {str(e)}")
            torch.cuda.empty_cache()
            continue
        except Exception as e:
            logger.error(f"Error in batch {batch_idx}: {str(e)}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            continue
    
    avg_loss = total_loss / max(num_batches, 1)
    logger.info(f'Epoch {epoch} - Avg Loss: {avg_loss:.4f}, Samples: {num_samples}')
    
    return avg_loss


def validate_epoch(model, dataloader, device, logger, epoch, evaluator, log_dir):
    """Validate for one epoch with PoseTrack21 multi-person metrics."""
    model.eval()
    
    total_samples = 0
    total_val_loss = 0.0
    num_batches = 0
    
    # Reset evaluator
    evaluator.reset()
    
    # Create validation criterion
    val_criterion = RLELoss(use_target_weight=True, size_average=True)
    
    # 시각화 결과 저장 디렉토리 생성
    vis_dir = os.path.join(log_dir, 'visualizations', f'epoch_{epoch}')
    os.makedirs(vis_dir, exist_ok=True)
    visualized_once = False
    
    with torch.no_grad():
        pbar = tqdm(dataloader, desc=f'Validation Epoch {epoch}')
        
        for batch_idx, batch_data in enumerate(pbar):
            try:
                # Clear GPU cache periodically
                if batch_idx % 10 == 0:
                    torch.cuda.empty_cache()
                
                # Extract data
                images_tensor = batch_data['images']
                raw_images_paths = batch_data['image_paths']
                poses = batch_data['poses']
                pose_weights = batch_data['pose_weights']
                frame_info = batch_data['frame_info']
                
                if len(images_tensor) == 0:
                    continue
                
                # Process only 1 image at a time to avoid OOM
                max_chunk_size = 1  # Process only 1 image at once for safety
                
                if len(images_tensor) > max_chunk_size:
                    # Split batch into smaller chunks
                    chunks = []
                    for i in range(0, len(images_tensor), max_chunk_size):
                        end_idx = min(i + max_chunk_size, len(images_tensor))
                        chunk = {
                            'images': images_tensor[i:end_idx],
                            'poses': poses[i:end_idx],
                            'pose_weights': pose_weights[i:end_idx],
                            'frame_info': frame_info[i:end_idx] if isinstance(frame_info, list) else [frame_info],
                            'image_paths': raw_images_paths[i:end_idx] if isinstance(raw_images_paths, list) else [raw_images_paths]
                        }
                        chunks.append(chunk)
                else:
                    chunks = [batch_data]
                
                # Process each chunk
                all_detection_results = []
                
                for chunk in chunks:
                    # Move to device
                    images_device = chunk['images'].to(device)
                    
                    # Forward pass with tracking mode for multi-person
                    tracking_results = model(images_device, mode='track')
                    all_detection_results.extend(tracking_results)
                    
                    # Clear intermediate tensors
                    del images_device
                    torch.cuda.empty_cache()
                
                # Extract predictions for evaluation
                pred_poses_orig = []
                pred_scores_list = []
                pred_track_ids_list = []
                
                for img_idx, tracks in enumerate(all_detection_results):
                    if len(tracks) > 0:
                        # Multiple persons tracked
                        img_poses = np.array([track.get('pose_mu', track.get('pose', np.zeros((17, 2)))) for track in tracks])
                        img_scores = np.array([track.get('score', 1.0) for track in tracks])
                        img_track_ids = [track.get('track_id', i) for i, track in enumerate(tracks)]
                    else:
                        # No person tracked - create dummy prediction
                        img_poses = np.zeros((1, 17, 2))
                        img_scores = np.array([0.0])
                        img_track_ids = [0]
                    
                    pred_poses_orig.append(img_poses)
                    pred_scores_list.append(img_scores)
                    pred_track_ids_list.append(img_track_ids)
                
                # Prepare ground truth
                gt_poses_normalized = poses.cpu().numpy()
                gt_weights = pose_weights.cpu().numpy()
                
                gt_poses_orig = []
                for i in range(len(gt_poses_normalized)):
                    try:
                        if i < len(frame_info) and 'image_size' in frame_info[i]:
                            img_w, img_h = frame_info[i]['image_size']
                        else:
                            img_w, img_h = 640, 480
                        
                        # Transform GT poses to original scale
                        if len(gt_poses_normalized[i].shape) == 2:
                            gt_p = gt_poses_normalized[i:i+1].copy()
                        else:
                            gt_p = gt_poses_normalized[i].copy()
                        
                        gt_p[..., 0] *= img_w
                        gt_p[..., 1] *= img_h
                        gt_poses_orig.append(gt_p)
                    except Exception as e:
                        logger.warning(f"Error processing GT sample {i}: {str(e)}")
                        continue

                # Prepare data for evaluator
                if len(pred_poses_orig) > 0 and len(gt_poses_orig) > 0:
                    predictions_eval = {
                        'poses': pred_poses_orig,
                        'scores': pred_scores_list,
                        'track_ids': pred_track_ids_list  # Use actual track IDs from tracker
                    }
                    
                    ground_truth_eval = {
                        'poses': gt_poses_orig,
                        'track_ids': [list(range(p.shape[0])) for p in gt_poses_orig],
                        'visibility': [gt_weights[i, :, 0] if i < len(gt_weights) and len(gt_weights[i].shape) > 1 else (gt_weights[i] if i < len(gt_weights) else None) for i in range(len(gt_poses_orig))]
                    }
                    
                    valid_frame_info = frame_info[:len(pred_poses_orig)]
                    
                    # Add to evaluator
                    evaluator.add_batch(predictions_eval, ground_truth_eval, valid_frame_info)

                # 시각화 (첫 번째 배치만)
                if (not visualized_once and batch_idx == 0 and len(raw_images_paths) > 0 and 
                    len(pred_poses_orig) > 0 and len(gt_poses_orig) > 0 and VISUALIZATION_AVAILABLE):
                    try:
                        img_path_to_vis = raw_images_paths[0][0] if isinstance(raw_images_paths[0], list) else raw_images_paths[0]
                        
                        visualize_pose_matching(
                            img_path_to_vis,
                            pred_poses_orig[0],
                            gt_poses_orig[0],
                            save_path=os.path.join(vis_dir, f'val_batch_{batch_idx}.png'),
                            joint_names=evaluator.joint_names if hasattr(evaluator, 'joint_names') else None
                        )
                        visualized_once = True
                        logger.info(f'Visualization saved to {vis_dir}/val_batch_{batch_idx}.png')
                    except Exception as viz_error:
                        logger.warning(f"Visualization failed: {str(viz_error)}")
                        pass
                
                total_samples += len(images_tensor)
                
                # Update progress bar  
                pbar.set_postfix({
                    'Samples': total_samples
                })
                
            except torch.cuda.OutOfMemoryError as e:
                logger.error(f"CUDA OOM in validation batch {batch_idx}: {str(e)}")
                torch.cuda.empty_cache()
                continue
            except Exception as e:
                logger.error(f"Error in validation batch {batch_idx}: {str(e)}")
                continue
    
    # Compute evaluation metrics
    eval_results = evaluator.evaluate()
    
    logger.info(f'Validation Epoch {epoch} - Samples: {total_samples}')
    logger.info(f'PCK: {eval_results["PCK"]:.3f}, mOKS: {eval_results["mOKS"]:.3f}')
    logger.info(f'MOTA: {eval_results["MOTA"]:.3f}, MOTP: {eval_results["MOTP"]:.3f}, IDF1: {eval_results["IDF1"]:.3f}')
    logger.info(f'Tracking - Misses: {eval_results["num_misses"]}, FP: {eval_results["num_false_positives"]}, Switches: {eval_results["num_switches"]}')
    
    return {
        'total_samples': total_samples, 
        'avg_loss': 0.0,  # Not computing validation loss for detection mode
        'PCK': eval_results['PCK'],
        'mOKS': eval_results['mOKS'],
        'MOTA': eval_results['MOTA'],
        'MOTP': eval_results['MOTP'],
        'IDF1': eval_results['IDF1'],
        'eval_results': eval_results
    }


def main():
    parser = argparse.ArgumentParser(description='Train MPPET-RLE with PoseTrack21')
    parser.add_argument('--data_root', type=str, default='datasets/PoseTrack21',
                       help='Path to PoseTrack21 dataset')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--epochs', type=int, default=50, help='Number of epochs')
    parser.add_argument('--lr', type=float, default=1e-4, help='Learning rate')
    parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay')
    parser.add_argument('--save_dir', type=str, default='checkpoints_posetrack21', help='Save directory')
    parser.add_argument('--log_dir', type=str, default='logs_posetrack21', help='Log directory')
    parser.add_argument('--device', type=str, default='cuda', help='Device to use')
    parser.add_argument('--eval_freq', type=int, default=5, help='Evaluation frequency (epochs)')
    
    args = parser.parse_args()
    
    # Setup directories
    os.makedirs(args.save_dir, exist_ok=True)
    
    # Setup logging
    logger = setup_logging(args.log_dir)
    logger.info(f'Starting PoseTrack21 training with args: {args}')
    
    # Device
    device = torch.device(args.device if torch.cuda.is_available() else 'cpu')
    logger.info(f'Using device: {device}')
    
    # Dataset configuration
    input_size = (192, 256)  # (width, height)
    
    train_transform = get_transform('train', input_size)
    val_transform = get_transform('val', input_size)
    
    train_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='train',
        transform=train_transform,
        sequence_length=2,  # Enable temporal data for tracking
        input_size=input_size,
        normalize_coords=True,
        use_gt_bbox=True
    )
    
    val_dataset = PoseTrackDataset(
        data_root=args.data_root,
        split='val',
        transform=val_transform,
        sequence_length=2,  # Enable temporal data for tracking
        input_size=input_size,
        normalize_coords=True,
        use_gt_bbox=True
    )
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=args.batch_size,
        shuffle=True,
        num_workers=2,  # Reduce workers to save memory
        collate_fn=collate_fn,
        pin_memory=False,  # Disable pin_memory to save GPU memory
        drop_last=True
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=1,  # Force batch size to 1 for validation to prevent OOM
        shuffle=False,
        num_workers=1,  # Reduce workers further
        collate_fn=collate_fn,
        pin_memory=False,  # Disable pin_memory to save GPU memory
        drop_last=False
    )
    
    logger.info(f'Train dataset: {len(train_dataset)} samples')
    logger.info(f'Val dataset: {len(val_dataset)} samples')
    
    # Model - Using MPPET-RLE for multi-person pose estimation and tracking
    model_config = {
        'pose_config': {
            'num_joints': 17,
            'input_size': input_size,
            'normalize_coords': True,
            'backbone': 'resnet50',
            'pretrained': True
        },
        'detection_config': {
            'detector_type': 'YOLOv5Detector',  # Use YOLOv5 for faster inference
            'model_name': 'yolov5s',    # Lightweight variant
            'score_threshold': 0.5,
            'nms_threshold': 0.5,
            'device': 'cuda'
        },
        'tracking_config': {
            'association_type': 'distribution_aware',
            'motion_model': 'kalman',
            'max_age': 30,
            'min_hits': 3,
            'bbox_weight': 0.3,
            'pose_weight': 0.7,
            'max_distance': 100.0,
            'uncertainty_threshold': 10.0
        },
        'joint_training': True,
        'pose_loss_weight': 1.0,
        'detection_loss_weight': 0.5
    }
    
    model = build_mppet_rle(model_config)
    model = model.to(device)
    
    # Loss function
    criterion = AdaptiveRLELoss(
        use_target_weight=True,
        size_average=True,
        residual_normalizer=1.0,
        q_dis='laplace',
        adaptive_weight=True
    )
    criterion = criterion.to(device)
    
    # Optimizer
    optimizer = optim.AdamW(model.parameters(), lr=args.lr, weight_decay=args.weight_decay)
    
    # Learning rate scheduler
    scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=args.epochs)
    
    # Evaluator
    evaluator = PoseTrack21Evaluator(num_joints=17)
    
    # Training loop
    best_pck = 0.0
    best_mota = 0.0  # Add MOTA tracking
    
    for epoch in range(args.epochs):
        logger.info(f'Starting epoch {epoch}/{args.epochs}')
        
        # Train
        train_loss = train_epoch(
            model, train_loader, optimizer, criterion, device, logger, epoch
        )
        
        # Validate
        if epoch % args.eval_freq == 0 or epoch == args.epochs - 1:
            val_metrics = validate_epoch(
                model, val_loader, device, logger, epoch, evaluator, args.log_dir # log_dir 전달
            )
            
            # Print detailed results
            evaluator.print_results(val_metrics['eval_results'])
            
            # Save best model based on combined PCK and MOTA score
            combined_score = 0.6 * val_metrics['PCK'] + 0.4 * val_metrics['MOTA']  # Weighted combination
            best_combined = 0.6 * best_pck + 0.4 * best_mota
            
            if combined_score > best_combined:
                best_pck = val_metrics['PCK']
                best_mota = val_metrics['MOTA']
                checkpoint = {
                    'epoch': epoch,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': train_loss,
                    'val_metrics': val_metrics,
                    'best_pck': best_pck,
                    'best_mota': best_mota,
                    'best_combined_score': combined_score
                }
                
                save_path = os.path.join(args.save_dir, 'best_model.pth')
                torch.save(checkpoint, save_path)
                logger.info(f'Saved best model with PCK {best_pck:.3f}, MOTA {best_mota:.3f} (combined: {combined_score:.3f})')
        
        # Update learning rate
        scheduler.step()
        
        # Save regular checkpoint
        if epoch % 10 == 0:
            checkpoint = {
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'train_loss': train_loss,
                'best_pck': best_pck,
                'best_mota': best_mota
            }
            
            save_path = os.path.join(args.save_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, save_path)
    
    logger.info('Training completed!')
    logger.info(f'Best PCK achieved: {best_pck:.3f}')
    logger.info(f'Best MOTA achieved: {best_mota:.3f}')


if __name__ == '__main__':
    main()
