#!/usr/bin/env python3
"""
PCK 계산 디버깅용 간단한 스크립트
"""

import numpy as np
import torch
from utils.posetrack21_metrics import PoseTrack21Evaluator

def test_pck_calculation():
    """PCK 계산이 올바르게 작동하는지 테스트"""
    print("="*60)
    print("PCK 계산 테스트")
    print("="*60)
    
    # 간단한 테스트 데이터 생성
    evaluator = PoseTrack21Evaluator(num_joints=17)
    
    # GT pose: 원점 중심의 간단한 포즈
    gt_pose = np.array([
        [100, 50],   # nose
        [100, 60],   # head_bottom  
        [100, 40],   # head_top
        [90, 50],    # left_ear
        [110, 50],   # right_ear
        [80, 100],   # left_shoulder
        [120, 100],  # right_shoulder
        [70, 130],   # left_elbow
        [130, 130],  # right_elbow
        [60, 160],   # left_wrist
        [140, 160],  # right_wrist
        [90, 180],   # left_hip
        [110, 180],  # right_hip
        [85, 220],   # left_knee
        [115, 220],  # right_knee
        [80, 260],   # left_ankle
        [120, 260]   # right_ankle
    ])
    
    # 테스트 케이스들
    test_cases = [
        ("Perfect match", gt_pose.copy()),
        ("Small noise (5px)", gt_pose + np.random.normal(0, 5, gt_pose.shape)),
        ("Medium noise (15px)", gt_pose + np.random.normal(0, 15, gt_pose.shape)),
        ("Large noise (30px)", gt_pose + np.random.normal(0, 30, gt_pose.shape)),
    ]
    
    for case_name, pred_pose in test_cases:
        print(f"\n{case_name}:")
        
        # 가짜 frame_info 생성
        frame_info = [{
            'vid_id': 'test_vid',
            'frame_id': 0,
            'image_size': (640, 480)
        }]
        
        # 가짜 predictions와 ground_truth 생성
        predictions = {
            'poses': [pred_pose.reshape(1, 17, 2)],  # 1 person, 17 joints, 2 coords
            'scores': [[1.0]],
            'track_ids': [[0]]
        }
        
        ground_truth = {
            'poses': [gt_pose.reshape(1, 17, 2)],
            'track_ids': [[0]],
            'visibility': [[np.ones(17)]]
        }
        
        # 평가
        evaluator.reset()
        evaluator.add_batch(predictions, ground_truth, frame_info)
        
        # 여러 방법으로 계산
        pck_result = evaluator.compute_pck(debug=True)
        pck_simple_10 = evaluator.compute_pck_simple(pixel_threshold=10, debug=True)
        pck_simple_25 = evaluator.compute_pck_simple(pixel_threshold=25, debug=True)
        
        print(f"  Head-size PCK: {pck_result['PCK']:.3f}")
        print(f"  Simple PCK (10px): {pck_simple_10['PCK_simple']:.3f}")
        print(f"  Simple PCK (25px): {pck_simple_25['PCK_simple']:.3f}")

def test_coordinate_scales():
    """좌표 스케일링 테스트"""
    print("\n" + "="*60)
    print("좌표 스케일링 테스트")
    print("="*60)
    
    # 정규화된 좌표 (0-1 범위)
    normalized_pose = np.array([
        [0.5, 0.25],   # 화면 중앙 상단
        [0.5, 0.3],    # 조금 아래
        [0.5, 0.2],    # 조금 위
        [0.45, 0.25],  # 왼쪽
        [0.55, 0.25],  # 오른쪽
        [0.4, 0.5],    # 등등...
        [0.6, 0.5],
        [0.35, 0.65],
        [0.65, 0.65],
        [0.3, 0.8],
        [0.7, 0.8],
        [0.45, 0.9],
        [0.55, 0.9],
        [0.425, 1.1],
        [0.575, 1.1],
        [0.4, 1.3],
        [0.6, 1.3]
    ]).clip(0, 1)  # 0-1 범위로 클리핑
    
    # 다양한 이미지 사이즈로 테스트
    image_sizes = [(192, 256), (640, 480), (1920, 1080)]
    
    for img_w, img_h in image_sizes:
        print(f"\nImage size: {img_w}x{img_h}")
        
        # 정규화된 좌표를 이미지 좌표로 변환
        pose_img_coords = normalized_pose.copy()
        pose_img_coords[:, 0] *= img_w
        pose_img_coords[:, 1] *= img_h
        
        print(f"  Normalized nose: {normalized_pose[0]}")
        print(f"  Image nose: {pose_img_coords[0]}")
        print(f"  Pose bbox: x=[{pose_img_coords[:, 0].min():.1f}, {pose_img_coords[:, 0].max():.1f}], "
              f"y=[{pose_img_coords[:, 1].min():.1f}, {pose_img_coords[:, 1].max():.1f}]")
        
        # Head size 계산
        evaluator = PoseTrack21Evaluator()
        head_size = evaluator._compute_head_size(pose_img_coords)
        pck_threshold = 0.5 * head_size
        
        print(f"  Head size: {head_size:.2f}px")
        print(f"  PCK threshold (50% head): {pck_threshold:.2f}px")

if __name__ == '__main__':
    test_pck_calculation()
    test_coordinate_scales()
