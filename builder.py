"""
Builder registry for SPPE (Single Person Pose Estimation), LOSS, DETECTOR, and TRACKER modules
Enhanced for MPPET-RLE End-to-End Training
"""

import torch
import torch.optim as optim
from torch.optim.lr_scheduler import MultiStepLR, StepLR, ExponentialLR, ReduceLROnPlateau


class Registry:
    """A registry to map strings to classes."""
    
    def __init__(self, name):
        self._name = name
        self._module_dict = {}
    
    def __len__(self):
        return len(self._module_dict)
    
    def __contains__(self, key):
        return self.get(key) is not None
    
    def __repr__(self):
        format_str = self.__class__.__name__ + f'(name={self._name}, items={list(self._module_dict.keys())})'
        return format_str
    
    @property
    def name(self):
        return self._name
    
    @property
    def module_dict(self):
        return self._module_dict
    
    def get(self, key):
        """Get the registry record."""
        return self._module_dict.get(key, None)
    
    def _register_module(self, module_class, module_name=None, force=False):
        if not isinstance(force, bool):
            raise TypeError(f'force must be a boolean, but got {type(force)}')
        
        if module_name is None:
            module_name = module_class.__name__
        
        if isinstance(module_name, str):
            module_name = [module_name]
        
        for name in module_name:
            if not force and name in self._module_dict:
                raise KeyError(f'{name} is already registered in {self.name}')
            self._module_dict[name] = module_class
    
    def register_module(self, name=None, force=False, module=None):
        """Register a module.
        
        A record will be added to `self._module_dict`, whose key is the class
        name or the specified name, and value is the class itself.
        It can be used as a decorator or a normal function.
        
        Args:
            name (str | None): The module name to be registered. If not
                specified, the class name will be used.
            force (bool, optional): Whether to override an existing class with
                the same name. Default: False.
            module (type): Module class to be registered.
        """
        if not isinstance(force, bool):
            raise TypeError(f'force must be a boolean, but got {type(force)}')
        
        # NOTE: This is a walkaround to be compatible with the old api
        if isinstance(name, type):
            return self.register_module(module=name)
        
        # raise the error ahead of time
        if not (name is None or isinstance(name, str) or isinstance(name, list)):
            raise TypeError(f'name must be either of None, an instance of str or a sequence of str, but got {type(name)}')
        
        # use it as a normal method: x.register_module(module=SomeClass)
        if module is not None:
            self._register_module(module_class=module, module_name=name, force=force)
            return module
        
        # use it as a decorator: @x.register_module()
        def _register(cls):
            self._register_module(module_class=cls, module_name=name, force=force)
            return cls
        
        return _register
    
    def build(self, cfg):
        """Build a module from config dict."""
        if not isinstance(cfg, dict):
            raise TypeError(f'cfg must be a dict, but got {type(cfg)}')
        
        if 'type' not in cfg:
            raise KeyError('cfg must contain the key "type"')
        
        cfg_ = cfg.copy()
        obj_type = cfg_.pop('type')
        
        if isinstance(obj_type, str):
            obj_cls = self.get(obj_type)
            if obj_cls is None:
                raise KeyError(f'{obj_type} is not in the {self.name} registry')
        elif isinstance(obj_type, type):
            obj_cls = obj_type
        else:
            raise TypeError(f'type must be a str or valid type, but got {type(obj_type)}')
        
        return obj_cls(**cfg_)


# Create registries
SPPE = Registry('sppe')
LOSS = Registry('loss')
DETECTOR = Registry('detector')
TRACKER = Registry('tracker')


def build_optimizer(cfg, parameters):
    """Build optimizer from config."""
    optimizer_type = cfg.get('type', 'Adam')
    
    if optimizer_type == 'Adam':
        optimizer = optim.Adam(
            parameters,
            lr=cfg.get('lr', 1e-3),
            weight_decay=cfg.get('weight_decay', 0),
            betas=cfg.get('betas', (0.9, 0.999)),
            eps=cfg.get('eps', 1e-8)
        )
    elif optimizer_type == 'AdamW':
        optimizer = optim.AdamW(
            parameters,
            lr=cfg.get('lr', 1e-3),
            weight_decay=cfg.get('weight_decay', 1e-2),
            betas=cfg.get('betas', (0.9, 0.999)),
            eps=cfg.get('eps', 1e-8)
        )
    elif optimizer_type == 'SGD':
        optimizer = optim.SGD(
            parameters,
            lr=cfg.get('lr', 1e-2),
            momentum=cfg.get('momentum', 0.9),
            weight_decay=cfg.get('weight_decay', 5e-4),
            nesterov=cfg.get('nesterov', True)
        )
    else:
        raise ValueError(f"Unsupported optimizer type: {optimizer_type}")
    
    return optimizer


def build_lr_scheduler(cfg, optimizer):
    """Build learning rate scheduler from config."""
    if cfg is None:
        return None
    
    scheduler_type = cfg.get('type', 'MultiStepLR')
    
    if scheduler_type == 'MultiStepLR':
        scheduler = MultiStepLR(
            optimizer,
            milestones=cfg.get('milestones', [20, 40]),
            gamma=cfg.get('gamma', 0.1)
        )
    elif scheduler_type == 'StepLR':
        scheduler = StepLR(
            optimizer,
            step_size=cfg.get('step_size', 10),
            gamma=cfg.get('gamma', 0.1)
        )
    elif scheduler_type == 'ExponentialLR':
        scheduler = ExponentialLR(
            optimizer,
            gamma=cfg.get('gamma', 0.95)
        )
    elif scheduler_type == 'ReduceLROnPlateau':
        scheduler = ReduceLROnPlateau(
            optimizer,
            mode=cfg.get('mode', 'min'),
            factor=cfg.get('factor', 0.5),
            patience=cfg.get('patience', 10),
            threshold=cfg.get('threshold', 1e-4),
            threshold_mode=cfg.get('threshold_mode', 'rel'),
            cooldown=cfg.get('cooldown', 0),
            min_lr=cfg.get('min_lr', 0),
            eps=cfg.get('eps', 1e-8)
        )
    else:
        raise ValueError(f"Unsupported scheduler type: {scheduler_type}")
    
    return scheduler
DETECTOR = Registry('detector')
TRACKER = Registry('tracker')

def build_sppe(cfg):
    """Build single person pose estimator."""
    return SPPE.build(cfg)

def build_loss(cfg):
    """Build loss function."""
    return LOSS.build(cfg)

def build_detector(cfg):
    """Build detector."""
    return DETECTOR.build(cfg)

def build_tracker(cfg):
    """Build tracker."""
    return TRACKER.build(cfg)
