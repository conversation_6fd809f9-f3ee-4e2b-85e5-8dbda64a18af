2025-06-12 12:26:49,805 - INFO - Starting PoseTrack21 training with args: Namespace(data_root='datasets/PoseTrack21', batch_size=4, epochs=50, lr=0.0001, weight_decay=0.0001, save_dir='checkpoints_posetrack21', log_dir='logs_posetrack21', device='cuda', eval_freq=5)
2025-06-12 12:26:49,809 - INFO - Using device: cuda
2025-06-12 12:26:52,238 - INFO - Train dataset: 17444 samples
2025-06-12 12:26:52,238 - INFO - Val dataset: 8735 samples
2025-06-12 12:26:52,821 - INFO - Starting epoch 0/50
2025-06-12 12:29:42,546 - INFO - Epoch 0 - Avg Loss: -0.2734, Samples: 114796
2025-06-12 12:29:43,363 - INFO - Visualization saved to logs_posetrack21/visualizations/epoch_0/val_batch_0.png
2025-06-12 12:29:53,033 - ERROR - Error in validation batch 18: CUDA out of memory. Tried to allocate 2.96 GiB. GPU 0 has a total capacity of 23.63 GiB of which 70.56 MiB is free. Including non-PyTorch memory, this process has 23.09 GiB memory in use. Of the allocated memory 16.57 GiB is allocated by PyTorch, and 6.06 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:29:53,936 - ERROR - Error in validation batch 19: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.10 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.52 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:29:55,086 - ERROR - Error in validation batch 20: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.10 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.52 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:29:55,694 - ERROR - Error in validation batch 21: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.10 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.52 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:29:56,300 - ERROR - Error in validation batch 22: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.10 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.52 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:29:56,905 - ERROR - Error in validation batch 23: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.52 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:29:57,512 - ERROR - Error in validation batch 24: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.52 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:29:58,119 - ERROR - Error in validation batch 25: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.52 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:09,103 - ERROR - Error in validation batch 54: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 12.92 GiB is allocated by PyTorch, and 7.67 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:11,539 - ERROR - Error in validation batch 57: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 12.92 GiB is allocated by PyTorch, and 7.67 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:13,889 - ERROR - Error in validation batch 60: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.10 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 12.92 GiB is allocated by PyTorch, and 7.67 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:14,343 - ERROR - Error in validation batch 61: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.10 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 12.92 GiB is allocated by PyTorch, and 7.67 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:28,385 - ERROR - Error in validation batch 94: CUDA out of memory. Tried to allocate 2.70 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.17 GiB is allocated by PyTorch, and 5.43 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:28,960 - ERROR - Error in validation batch 95: CUDA out of memory. Tried to allocate 2.70 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.17 GiB is allocated by PyTorch, and 5.43 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:29,496 - ERROR - Error in validation batch 96: CUDA out of memory. Tried to allocate 2.70 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 15.17 GiB is allocated by PyTorch, and 5.43 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:30,010 - ERROR - Error in validation batch 97: CUDA out of memory. Tried to allocate 2.60 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 14.61 GiB is allocated by PyTorch, and 5.99 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:30,517 - ERROR - Error in validation batch 98: CUDA out of memory. Tried to allocate 2.54 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.11 GiB is free. Including non-PyTorch memory, this process has 21.06 GiB memory in use. Of the allocated memory 14.33 GiB is allocated by PyTorch, and 6.27 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:30,865 - ERROR - Error in validation batch 99: CUDA out of memory. Tried to allocate 3.43 GiB. GPU 0 has a total capacity of 23.63 GiB of which 1.53 GiB is free. Including non-PyTorch memory, this process has 21.63 GiB memory in use. Of the allocated memory 16.52 GiB is allocated by PyTorch, and 4.65 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:31,799 - ERROR - Error in validation batch 100: CUDA out of memory. Tried to allocate 4.31 GiB. GPU 0 has a total capacity of 23.63 GiB of which 1.44 GiB is free. Including non-PyTorch memory, this process has 21.73 GiB memory in use. Of the allocated memory 17.40 GiB is allocated by PyTorch, and 3.87 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:32,906 - ERROR - Error in validation batch 101: CUDA out of memory. Tried to allocate 7.58 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 17.24 GiB is allocated by PyTorch, and 1.95 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,005 - ERROR - Error in validation batch 102: CUDA out of memory. Tried to allocate 7.37 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 16.79 GiB is allocated by PyTorch, and 2.41 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,046 - ERROR - Error in validation batch 103: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,085 - ERROR - Error in validation batch 104: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,131 - ERROR - Error in validation batch 105: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,170 - ERROR - Error in validation batch 106: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,216 - ERROR - Error in validation batch 107: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,255 - ERROR - Error in validation batch 108: CUDA out of memory. Tried to allocate 4.51 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.52 GiB is allocated by PyTorch, and 8.68 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,300 - ERROR - Error in validation batch 109: CUDA out of memory. Tried to allocate 4.51 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.52 GiB is allocated by PyTorch, and 8.68 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,341 - ERROR - Error in validation batch 110: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,380 - ERROR - Error in validation batch 111: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,420 - ERROR - Error in validation batch 112: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:30:33,459 - ERROR - Error in validation batch 113: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.50 GiB is free. Including non-PyTorch memory, this process has 19.66 GiB memory in use. Of the allocated memory 10.63 GiB is allocated by PyTorch, and 8.56 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
