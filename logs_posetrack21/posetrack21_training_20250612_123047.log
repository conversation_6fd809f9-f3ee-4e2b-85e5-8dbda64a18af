2025-06-12 12:30:47,199 - INFO - Starting PoseTrack21 training with args: Namespace(data_root='datasets/PoseTrack21', batch_size=4, epochs=50, lr=0.0001, weight_decay=0.0001, save_dir='checkpoints_posetrack21', log_dir='logs_posetrack21', device='cuda', eval_freq=5)
2025-06-12 12:30:47,203 - INFO - Using device: cuda
2025-06-12 12:30:49,544 - INFO - Train dataset: 17444 samples
2025-06-12 12:30:49,544 - INFO - Val dataset: 8735 samples
2025-06-12 12:30:50,149 - INFO - Starting epoch 0/50
2025-06-12 12:33:39,796 - INFO - Epoch 0 - Avg Loss: -0.2731, Samples: 114796
2025-06-12 12:33:41,068 - INFO - Visualization saved to logs_posetrack21/visualizations/epoch_0/val_batch_0.png
2025-06-12 12:33:53,692 - ERROR - Error in validation batch 18: CUDA out of memory. Tried to allocate 2.96 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.95 GiB is free. Including non-PyTorch memory, this process has 20.21 GiB memory in use. Of the allocated memory 14.36 GiB is allocated by PyTorch, and 5.39 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:33:54,525 - ERROR - Error in validation batch 19: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.02 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.59 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:33:55,650 - ERROR - Error in validation batch 20: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.02 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.59 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:33:56,258 - ERROR - Error in validation batch 21: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.02 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.59 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:33:56,863 - ERROR - Error in validation batch 22: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.59 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:33:57,471 - ERROR - Error in validation batch 23: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.59 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:33:58,076 - ERROR - Error in validation batch 24: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.59 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:33:58,682 - ERROR - Error in validation batch 25: CUDA out of memory. Tried to allocate 3.11 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.08 GiB is allocated by PyTorch, and 5.59 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:12,849 - ERROR - Error in validation batch 54: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.02 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 12.93 GiB is allocated by PyTorch, and 7.74 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:15,399 - ERROR - Error in validation batch 57: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.02 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 12.93 GiB is allocated by PyTorch, and 7.74 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:17,991 - ERROR - Error in validation batch 60: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 12.93 GiB is allocated by PyTorch, and 7.74 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:18,444 - ERROR - Error in validation batch 61: CUDA out of memory. Tried to allocate 2.28 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 12.93 GiB is allocated by PyTorch, and 7.74 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:35,110 - ERROR - Error in validation batch 94: CUDA out of memory. Tried to allocate 2.70 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.17 GiB is allocated by PyTorch, and 5.50 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:35,673 - ERROR - Error in validation batch 95: CUDA out of memory. Tried to allocate 2.70 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.17 GiB is allocated by PyTorch, and 5.50 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:36,209 - ERROR - Error in validation batch 96: CUDA out of memory. Tried to allocate 2.70 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 15.17 GiB is allocated by PyTorch, and 5.50 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:36,724 - ERROR - Error in validation batch 97: CUDA out of memory. Tried to allocate 2.60 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 14.61 GiB is allocated by PyTorch, and 6.06 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:37,229 - ERROR - Error in validation batch 98: CUDA out of memory. Tried to allocate 2.54 GiB. GPU 0 has a total capacity of 23.63 GiB of which 2.03 GiB is free. Including non-PyTorch memory, this process has 21.13 GiB memory in use. Of the allocated memory 14.33 GiB is allocated by PyTorch, and 6.34 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:37,571 - ERROR - Error in validation batch 99: CUDA out of memory. Tried to allocate 3.43 GiB. GPU 0 has a total capacity of 23.63 GiB of which 1.46 GiB is free. Including non-PyTorch memory, this process has 21.71 GiB memory in use. Of the allocated memory 16.53 GiB is allocated by PyTorch, and 4.72 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:38,539 - ERROR - Error in validation batch 100: CUDA out of memory. Tried to allocate 4.31 GiB. GPU 0 has a total capacity of 23.63 GiB of which 1.36 GiB is free. Including non-PyTorch memory, this process has 21.81 GiB memory in use. Of the allocated memory 17.40 GiB is allocated by PyTorch, and 3.94 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,668 - ERROR - Error in validation batch 101: CUDA out of memory. Tried to allocate 7.58 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 17.25 GiB is allocated by PyTorch, and 2.02 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,746 - ERROR - Error in validation batch 102: CUDA out of memory. Tried to allocate 7.37 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 16.80 GiB is allocated by PyTorch, and 2.48 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,786 - ERROR - Error in validation batch 103: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,826 - ERROR - Error in validation batch 104: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,867 - ERROR - Error in validation batch 105: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,907 - ERROR - Error in validation batch 106: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,955 - ERROR - Error in validation batch 107: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:39,995 - ERROR - Error in validation batch 108: CUDA out of memory. Tried to allocate 4.51 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.52 GiB is allocated by PyTorch, and 8.75 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:40,036 - ERROR - Error in validation batch 109: CUDA out of memory. Tried to allocate 4.51 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.52 GiB is allocated by PyTorch, and 8.75 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:40,076 - ERROR - Error in validation batch 110: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:40,116 - ERROR - Error in validation batch 111: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:40,155 - ERROR - Error in validation batch 112: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
2025-06-12 12:34:40,195 - ERROR - Error in validation batch 113: CUDA out of memory. Tried to allocate 4.57 GiB. GPU 0 has a total capacity of 23.63 GiB of which 3.44 GiB is free. Including non-PyTorch memory, this process has 19.73 GiB memory in use. Of the allocated memory 10.64 GiB is allocated by PyTorch, and 8.63 GiB is reserved by PyTorch but unallocated. If reserved but unallocated memory is large try setting PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True to avoid fragmentation.  See documentation for Memory Management  (https://pytorch.org/docs/stable/notes/cuda.html#environment-variables)
