"""
Base configuration for MPPET-RLE
"""

from easydict import EasyDict

# Base configuration
base_config = EasyDict()

# Model configuration
base_config.model = EasyDict()
base_config.model.type = 'MPPET_RLE'
base_config.model.joint_training = True
base_config.model.pose_loss_weight = 1.0
base_config.model.detection_loss_weight = 1.0

# Pose estimation configuration
base_config.pose = EasyDict()
base_config.pose.num_joints = 17
base_config.pose.input_size = (192, 256)  # (width, height)
base_config.pose.normalize_coords = True
base_config.pose.backbone = 'resnet50'
base_config.pose.pretrained = True

# Detection configuration
base_config.detection = EasyDict()
base_config.detection.pretrained = True
base_config.detection.num_classes = 2  # background + person
base_config.detection.score_threshold = 0.5
base_config.detection.nms_threshold = 0.5

# Tracking configuration
base_config.tracking = EasyDict()
base_config.tracking.association_type = 'distribution_aware'
base_config.tracking.motion_model = 'kalman'
base_config.tracking.max_age = 30
base_config.tracking.min_hits = 3
base_config.tracking.bbox_weight = 0.3
base_config.tracking.pose_weight = 0.7
base_config.tracking.max_distance = 100.0
base_config.tracking.uncertainty_threshold = 10.0

# Loss configuration
base_config.loss = EasyDict()
base_config.loss.type = 'AdaptiveRLELoss'
base_config.loss.use_target_weight = True
base_config.loss.size_average = True
base_config.loss.residual_normalizer = 1.0
base_config.loss.q_dis = 'laplace'
base_config.loss.adaptive_weight = True

# Training configuration
base_config.train = EasyDict()
base_config.train.batch_size = 16
base_config.train.num_epochs = 50
base_config.train.learning_rate = 1e-4
base_config.train.weight_decay = 1e-4
base_config.train.grad_clip_norm = 1.0
base_config.train.save_interval = 10
base_config.train.val_interval = 1

# Optimizer configuration
base_config.optimizer = EasyDict()
base_config.optimizer.type = 'Adam'
base_config.optimizer.lr = 1e-4
base_config.optimizer.weight_decay = 1e-4
base_config.optimizer.betas = (0.9, 0.999)

# Scheduler configuration
base_config.scheduler = EasyDict()
base_config.scheduler.type = 'StepLR'
base_config.scheduler.step_size = 20
base_config.scheduler.gamma = 0.1

# Data augmentation
base_config.augmentation = EasyDict()
base_config.augmentation.rotation = 30
base_config.augmentation.scale = 0.25
base_config.augmentation.flip = True
base_config.augmentation.color_jitter = True

# Evaluation configuration
base_config.eval = EasyDict()
base_config.eval.pck_threshold = 0.2
base_config.eval.pckh_threshold = 0.5
base_config.eval.save_predictions = True

# Paths
base_config.paths = EasyDict()
base_config.paths.data_root = '/path/to/posetrack21'
base_config.paths.checkpoint_dir = 'checkpoints_posetrack21'
base_config.paths.log_dir = 'logs_posetrack21'
base_config.paths.visualization_dir = 'logs_posetrack21/visualizations'
