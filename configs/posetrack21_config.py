"""
PoseTrack21 specific configuration
"""

from .base_config import base_config
from easydict import EasyDict
import copy

# Create PoseTrack21 specific config
posetrack21_config = copy.deepcopy(base_config)

# Dataset specific settings
posetrack21_config.dataset = EasyDict()
posetrack21_config.dataset.name = 'PoseTrack21'
posetrack21_config.dataset.data_root = '/home/<USER>/workspace/pktrack/datasets/PoseTrack21'
posetrack21_config.dataset.sequence_length = 2
posetrack21_config.dataset.use_gt_bbox = True
posetrack21_config.dataset.normalize_coords = True

# PoseTrack21 specific keypoints (17 joints)
posetrack21_config.dataset.keypoints = [
    "nose", "head_bottom", "head_top", "left_ear", "right_ear",
    "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
    "left_wrist", "right_wrist", "left_hip", "right_hip",
    "left_knee", "right_knee", "left_ankle", "right_ankle"
]

# PoseTrack21 specific training settings
posetrack21_config.train.batch_size = 8  # Reduced for multi-person data
posetrack21_config.train.num_epochs = 100
posetrack21_config.train.learning_rate = 5e-5  # Lower learning rate for fine-tuning
posetrack21_config.train.accumulate_grad_batches = 2  # Gradient accumulation

# PoseTrack21 specific validation settings
posetrack21_config.eval.metrics = ['PCK', 'PCKh', 'MOTA', 'MOTP', 'IDF1']
posetrack21_config.eval.pck_threshold = 0.2
posetrack21_config.eval.pckh_threshold = 0.5

# Data loading settings
posetrack21_config.data = EasyDict()
posetrack21_config.data.num_workers = 4
posetrack21_config.data.pin_memory = True
posetrack21_config.data.persistent_workers = True

# Tracking specific settings for PoseTrack21
posetrack21_config.tracking.max_age = 30  # Frames
posetrack21_config.tracking.min_hits = 3
posetrack21_config.tracking.iou_threshold = 0.3
posetrack21_config.tracking.pose_similarity_threshold = 0.7

# Logging and visualization
posetrack21_config.logging = EasyDict()
posetrack21_config.logging.log_interval = 100
posetrack21_config.logging.visualize_interval = 500
posetrack21_config.logging.save_visualization = True

# Model specific adjustments for PoseTrack21
posetrack21_config.pose.backbone = 'resnet50'
posetrack21_config.pose.pretrained = True
posetrack21_config.pose.input_size = (192, 256)

# Loss weights for multi-person scenarios
posetrack21_config.loss.person_weight = True
posetrack21_config.loss.uncertainty_weight = 0.1
posetrack21_config.loss.coord_weight = 1.0
