"""
Configuration for MPPET-RLE End-to-End Training
"""

import os
from easydict import EasyDict as edict


def get_config():
    """Get configuration for MPPET-RLE end-to-end training."""
    cfg = edict()
    
    # Data configuration
    cfg.data = edict()
    cfg.data.dataset = 'PoseTrack21'
    cfg.data.train_data_dir = 'datasets/PoseTrack21/images/train'
    cfg.data.val_data_dir = 'datasets/PoseTrack21/images/val'
    cfg.data.train_ann_file = 'datasets/PoseTrack21/annotations/train'
    cfg.data.val_ann_file = 'datasets/PoseTrack21/annotations/val'
    cfg.data.image_size = (256, 192)  # (width, height)
    cfg.data.heatmap_size = (64, 48)  # (width, height)
    cfg.data.num_joints = 17
    cfg.data.subset_ratio = 1.0  # Use full training set
    cfg.data.val_subset_ratio = 0.2  # Use 20% of validation set for faster validation
    
    # Data augmentation
    cfg.data.train_transform = edict()
    cfg.data.train_transform.scale_factor = 0.25
    cfg.data.train_transform.rotation_factor = 30
    cfg.data.train_transform.flip_prob = 0.5
    cfg.data.train_transform.color_jitter = True
    
    cfg.data.val_transform = edict()
    cfg.data.val_transform.scale_factor = 0.0
    cfg.data.val_transform.rotation_factor = 0
    cfg.data.val_transform.flip_prob = 0.0
    cfg.data.val_transform.color_jitter = False
    
    # Model configuration
    cfg.model = edict()
    cfg.model.joint_training = True  # Enable end-to-end training
    cfg.model.pose_loss_weight = 1.0
    cfg.model.detection_loss_weight = 0.5  # Lower weight for detection loss
    
    # Pose estimation configuration
    cfg.model.pose_config = edict()
    cfg.model.pose_config.num_joints = 17
    cfg.model.pose_config.input_size = (192, 256)  # (width, height)
    cfg.model.pose_config.backbone = 'resnet50'
    cfg.model.pose_config.pretrained = True
    cfg.model.pose_config.normalize_coords = False
    
    # Detection configuration
    cfg.model.detection_config = edict()
    cfg.model.detection_config.pretrained = True
    cfg.model.detection_config.num_classes = 2  # background + person
    cfg.model.detection_config.score_threshold = 0.5
    cfg.model.detection_config.nms_threshold = 0.5
    
    # Tracking configuration
    cfg.model.tracking_config = edict()
    cfg.model.tracking_config.association_type = 'distribution_aware'
    cfg.model.tracking_config.motion_model = 'kalman'
    cfg.model.tracking_config.max_age = 30
    cfg.model.tracking_config.min_hits = 3
    cfg.model.tracking_config.bbox_weight = 0.3
    cfg.model.tracking_config.pose_weight = 0.7
    cfg.model.tracking_config.max_distance = 100.0
    cfg.model.tracking_config.uncertainty_threshold = 10.0
    
    # RLE Loss configuration
    cfg.model.rle_loss_config = edict()
    cfg.model.rle_loss_config.use_target_weight = True
    cfg.model.rle_loss_config.size_average = True
    cfg.model.rle_loss_config.residual_normalizer = 1.0
    cfg.model.rle_loss_config.q_dis = 'laplace'  # 'laplace' or 'gaussian'
    
    # Training configuration
    cfg.training = edict()
    cfg.training.num_epochs = 50
    cfg.training.batch_size = 8  # Smaller batch size for end-to-end training
    cfg.training.val_batch_size = 4
    cfg.training.gradient_clip_norm = 1.0
    cfg.training.early_stopping_patience = 10
    
    # Optimizer configuration
    cfg.optimizer = edict()
    cfg.optimizer.type = 'AdamW'
    cfg.optimizer.lr = 1e-4  # Lower learning rate for stable training
    cfg.optimizer.weight_decay = 1e-4
    cfg.optimizer.betas = (0.9, 0.999)
    cfg.optimizer.eps = 1e-8
    
    # Learning rate scheduler configuration
    cfg.lr_scheduler = edict()
    cfg.lr_scheduler.type = 'MultiStepLR'
    cfg.lr_scheduler.milestones = [20, 35, 45]
    cfg.lr_scheduler.gamma = 0.1
    
    # Alternative: ReduceLROnPlateau
    # cfg.lr_scheduler.type = 'ReduceLROnPlateau'
    # cfg.lr_scheduler.mode = 'min'
    # cfg.lr_scheduler.factor = 0.5
    # cfg.lr_scheduler.patience = 5
    # cfg.lr_scheduler.threshold = 1e-4
    
    # Logging configuration
    cfg.logging = edict()
    cfg.logging.log_dir = 'logs_mppet_rle_endtoend'
    cfg.logging.checkpoint_dir = 'checkpoints_mppet_rle_endtoend'
    cfg.logging.save_freq = 5  # Save checkpoint every 5 epochs
    cfg.logging.print_freq = 50  # Print training progress every 50 batches
    
    # Evaluation configuration
    cfg.evaluation = edict()
    cfg.evaluation.metrics = ['PCK', 'MOTA', 'MOTP']
    cfg.evaluation.pck_threshold = 0.5
    cfg.evaluation.tracking_threshold = 0.5
    
    return cfg
