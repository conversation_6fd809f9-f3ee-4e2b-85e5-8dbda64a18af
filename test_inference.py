#!/usr/bin/env python3
"""
Simple inference test for MPPET-RLE
"""

import torch
import numpy as np
from models.mppet_rle import build_mppet_rle
from configs.mppet_rle_endtoend_config import get_config

def test_inference():
    """Test inference with dummy data."""
    
    print("Loading model configuration...")
    cfg = get_config()
    
    print("Building model...")
    model = build_mppet_rle(cfg.model)
    
    # Load checkpoint
    checkpoint_path = "checkpoints/checkpoint_epoch_1.pth"
    print(f"Loading checkpoint: {checkpoint_path}")
    
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    model.load_state_dict(checkpoint['model_state_dict'])
    
    # Set to eval mode
    model.eval()
    
    # Create dummy input
    dummy_image = torch.randn(3, 256, 192)  # Single image
    images = [dummy_image]
    
    print("Running inference...")
    with torch.no_grad():
        # Test detection + pose estimation
        results = model(images, mode='detect_pose')
        print(f"Detection+Pose results: {len(results)} images")
        for i, img_results in enumerate(results):
            print(f"  Image {i}: {len(img_results)} detections")
            for j, detection in enumerate(img_results):
                print(f"    Detection {j}: bbox shape {detection['bbox'].shape}, pose shape {detection['pose_mu'].shape}")
        
        # Test tracking
        track_results = model(images, mode='track')
        print(f"Tracking results: {len(track_results)} images")
        for i, img_tracks in enumerate(track_results):
            print(f"  Image {i}: {len(img_tracks)} tracks")
    
    print("Inference test completed successfully! ✅")

if __name__ == '__main__':
    test_inference()
