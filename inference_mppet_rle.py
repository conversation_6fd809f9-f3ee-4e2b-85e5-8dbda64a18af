#!/usr/bin/env python3
"""
Inference Script for MPPET-RLE
End-to-End Multi-Person Pose Estimation and Tracking
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path

import torch
import torch.nn.functional as F
import cv2
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.mppet_rle import build_mppet_rle
from configs.mppet_rle_endtoend_config import get_config
from utils.visualization import draw_pose_with_uncertainty, draw_tracking_results
from datasets.posetrack_dataset import PoseTrackDataset


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='MPPET-RLE Inference')
    parser.add_argument('--checkpoint', type=str, required=True,
                       help='Path to model checkpoint')
    parser.add_argument('--input', type=str, required=True,
                       help='Input image/video path or directory')
    parser.add_argument('--output', type=str, default='output',
                       help='Output directory')
    parser.add_argument('--mode', type=str, choices=['detect_pose', 'track'], 
                       default='detect_pose',
                       help='Inference mode: detect_pose or track')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--batch-size', type=int, default=1,
                       help='Batch size for inference')
    parser.add_argument('--save-json', action='store_true',
                       help='Save results in JSON format')
    parser.add_argument('--visualize', action='store_true',
                       help='Save visualization images')
    parser.add_argument('--uncertainty-threshold', type=float, default=10.0,
                       help='Uncertainty threshold for visualization')
    
    return parser.parse_args()


def setup_device(device_arg):
    """Setup computing device."""
    if device_arg == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_arg)
    
    return device


def load_model(checkpoint_path, device):
    """Load model from checkpoint."""
    print(f"Loading model from: {checkpoint_path}")
    
    # Load configuration
    cfg = get_config()
    
    # Build model
    model = build_mppet_rle(cfg.model)
    
    # Load checkpoint
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    model.to(device)
    model.eval()
    
    print("Model loaded successfully")
    return model, cfg


def preprocess_image(image_path, target_size=(256, 192)):
    """
    Preprocess image for inference.
    
    Args:
        image_path: Path to input image
        target_size: Target size (width, height)
        
    Returns:
        Tuple of (processed_tensor, original_image, scale_factors)
    """
    # Load image
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"Could not load image: {image_path}")
    
    original_image = image.copy()
    original_h, original_w = image.shape[:2]
    
    # Convert BGR to RGB
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # Resize image
    image = cv2.resize(image, target_size)
    
    # Normalize to [0, 1]
    image = image.astype(np.float32) / 255.0
    
    # Normalize with ImageNet stats
    mean = np.array([0.485, 0.456, 0.406])
    std = np.array([0.229, 0.224, 0.225])
    image = (image - mean) / std
    
    # Convert to tensor and add batch dimension
    image_tensor = torch.from_numpy(image).permute(2, 0, 1).unsqueeze(0)
    
    # Scale factors for coordinate conversion
    scale_x = original_w / target_size[0]
    scale_y = original_h / target_size[1]
    
    return image_tensor, original_image, (scale_x, scale_y)


def postprocess_results(results, scale_factors, original_size):
    """
    Postprocess model results to original image coordinates.
    
    Args:
        results: Model output results
        scale_factors: (scale_x, scale_y) for coordinate conversion
        original_size: (width, height) of original image
        
    Returns:
        Processed results with coordinates in original image space
    """
    scale_x, scale_y = scale_factors
    
    processed_results = []
    
    for img_results in results:
        processed_img_results = []
        
        for detection in img_results:
            processed_detection = detection.copy()
            
            # Scale bounding box
            bbox = detection['bbox']
            processed_detection['bbox'] = [
                bbox[0] * scale_x,
                bbox[1] * scale_y,
                bbox[2] * scale_x,
                bbox[3] * scale_y
            ]
            
            # Scale pose coordinates
            pose_mu = detection['pose_mu']
            pose_sigma = detection['pose_sigma']
            
            processed_detection['pose_mu'] = pose_mu * np.array([scale_x, scale_y])
            processed_detection['pose_sigma'] = pose_sigma * np.array([scale_x, scale_y])
            
            processed_img_results.append(processed_detection)
        
        processed_results.append(processed_img_results)
    
    return processed_results


def run_single_image_inference(model, image_path, args, device):
    """Run inference on a single image."""
    # Preprocess image
    image_tensor, original_image, scale_factors = preprocess_image(image_path)
    image_tensor = image_tensor.to(device)
    
    # Run inference
    with torch.no_grad():
        if args.mode == 'detect_pose':
            results = model([image_tensor.squeeze(0)], mode='detect_pose')
        elif args.mode == 'track':
            results = model([image_tensor.squeeze(0)], mode='track')
    
    # Postprocess results
    original_h, original_w = original_image.shape[:2]
    processed_results = postprocess_results(results, scale_factors, (original_w, original_h))
    
    return processed_results, original_image


def run_video_inference(model, video_path, args, device):
    """Run inference on a video file."""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise ValueError(f"Could not open video: {video_path}")
    
    frames = []
    frame_results = []
    
    # Reset tracker for video sequence
    if args.mode == 'track':
        model.reset_tracker()
    
    frame_idx = 0
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        print(f"Processing frame {frame_idx}")
        
        # Save frame as temporary image for processing
        temp_image_path = f"/tmp/temp_frame_{frame_idx}.jpg"
        cv2.imwrite(temp_image_path, frame)
        
        # Run inference
        results, original_frame = run_single_image_inference(
            model, temp_image_path, args, device
        )
        
        frames.append(original_frame)
        frame_results.append(results[0])  # Single image results
        
        # Clean up
        os.remove(temp_image_path)
        frame_idx += 1
    
    cap.release()
    return frame_results, frames


def save_results_json(results, output_path):
    """Save results in JSON format."""
    # Convert numpy arrays to lists for JSON serialization
    json_results = []
    
    for img_idx, img_results in enumerate(results):
        img_data = {
            'image_id': img_idx,
            'detections': []
        }
        
        for det in img_results:
            det_data = {
                'bbox': det['bbox'].tolist() if isinstance(det['bbox'], np.ndarray) else det['bbox'],
                'score': float(det['score']),
                'pose_mu': det['pose_mu'].tolist(),
                'pose_sigma': det['pose_sigma'].tolist(),
                'pose_confidence': det.get('pose_confidence', 1.0)
            }
            
            if 'track_id' in det:
                det_data['track_id'] = int(det['track_id'])
            
            img_data['detections'].append(det_data)
        
        json_results.append(img_data)
    
    with open(output_path, 'w') as f:
        json.dump(json_results, f, indent=2)
    
    print(f"Results saved to: {output_path}")


def visualize_results(results, images, output_dir, uncertainty_threshold=10.0):
    """Visualize results and save images."""
    os.makedirs(output_dir, exist_ok=True)
    
    for img_idx, (img_results, image) in enumerate(zip(results, images)):
        if len(img_results) == 0:
            continue
        
        # Create visualization
        vis_image = image.copy()
        
        for det in img_results:
            # Draw pose with uncertainty
            vis_image = draw_pose_with_uncertainty(
                vis_image,
                det['pose_mu'],
                det['pose_sigma'],
                uncertainty_threshold=uncertainty_threshold
            )
            
            # Draw bounding box
            bbox = det['bbox']
            cv2.rectangle(
                vis_image,
                (int(bbox[0]), int(bbox[1])),
                (int(bbox[2]), int(bbox[3])),
                (0, 255, 0), 2
            )
            
            # Add confidence score
            score_text = f"Score: {det['score']:.3f}"
            if 'pose_confidence' in det:
                score_text += f", Pose: {det['pose_confidence']:.3f}"
            
            cv2.putText(
                vis_image, score_text,
                (int(bbox[0]), int(bbox[1]) - 10),
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1
            )
            
            # Add track ID if available
            if 'track_id' in det:
                cv2.putText(
                    vis_image, f"ID: {det['track_id']}",
                    (int(bbox[0]), int(bbox[3]) + 20),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1
                )
        
        # Save visualization
        output_path = os.path.join(output_dir, f"result_{img_idx:06d}.jpg")
        cv2.imwrite(output_path, vis_image)
    
    print(f"Visualizations saved to: {output_dir}")


def main():
    """Main inference function."""
    args = parse_args()
    
    # Setup device
    device = setup_device(args.device)
    print(f"Using device: {device}")
    
    # Load model
    model, cfg = load_model(args.checkpoint, device)
    
    # Create output directory
    os.makedirs(args.output, exist_ok=True)
    
    # Determine input type
    input_path = Path(args.input)
    
    if input_path.is_file():
        if input_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']:
            # Single image
            print(f"Processing single image: {input_path}")
            results, images = run_single_image_inference(model, str(input_path), args, device)
            results = [results[0]]  # Wrap in list for consistency
            images = [images]
            
        elif input_path.suffix.lower() in ['.mp4', '.avi', '.mov', '.mkv']:
            # Video file
            print(f"Processing video: {input_path}")
            results, images = run_video_inference(model, str(input_path), args, device)
            
        else:
            raise ValueError(f"Unsupported file format: {input_path.suffix}")
    
    elif input_path.is_dir():
        # Directory of images
        print(f"Processing directory: {input_path}")
        image_files = sorted([f for f in input_path.glob("*") 
                            if f.suffix.lower() in ['.jpg', '.jpeg', '.png', '.bmp']])
        
        if len(image_files) == 0:
            raise ValueError(f"No image files found in: {input_path}")
        
        results = []
        images = []
        
        # Reset tracker for image sequence
        if args.mode == 'track':
            model.reset_tracker()
        
        for img_file in image_files:
            print(f"Processing: {img_file.name}")
            img_results, img = run_single_image_inference(model, str(img_file), args, device)
            results.append(img_results[0])
            images.append(img)
    
    else:
        raise ValueError(f"Input path does not exist: {input_path}")
    
    print(f"Processed {len(results)} images")
    
    # Save results
    if args.save_json:
        json_output_path = os.path.join(args.output, "results.json")
        save_results_json(results, json_output_path)
    
    # Visualize results
    if args.visualize:
        viz_output_dir = os.path.join(args.output, "visualizations")
        visualize_results(results, images, viz_output_dir, args.uncertainty_threshold)
    
    print("Inference completed!")


if __name__ == '__main__':
    main()
