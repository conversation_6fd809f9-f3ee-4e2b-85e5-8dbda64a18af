"""
MPPET-RLE: Multi-Person Pose Estimation and Tracking with Residual Log-likelihood Estimation
End-to-End Multi-Task Learning Framework
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from easydict import EasyDict

from .pose_estimator import SimpleR<PERSON>ress<PERSON><PERSON>
from .losses import <PERSON><PERSON><PERSON><PERSON>
from tracking.tracker import MP<PERSON><PERSON><PERSON>racker
from builder import SPPE


@SPPE.register_module
class MPPET_RLE(nn.Module):
    """
    End-to-End Multi-Person Pose Estimation and Tracking with RLE uncertainty.
    
    This model integrates:
    1. Person detection (Faster R-CNN)
    2. RLE-based pose estimation with uncertainty quantification
    3. Distribution-aware tracking using pose uncertainties
    4. Joint multi-task learning for detection and pose estimation
    """
    
    def __init__(self, 
                 # Pose estimation config
                 pose_config=None,
                 # Detection config
                 detection_config=None,
                 # Tracking config
                 tracking_config=None,
                 # Training config
                 joint_training=True,
                 pose_loss_weight=1.0,
                 detection_loss_weight=1.0,
                 rle_loss_config=None):
        """
        Initialize MPPET-RLE model.
        
        Args:
            pose_config: Configuration for RLE pose estimation
            detection_config: Configuration for person detection
            tracking_config: Configuration for tracking
            joint_training: Whether to train detection and pose jointly
            pose_loss_weight: Weight for pose estimation loss
            detection_loss_weight: Weight for detection loss
            rle_loss_config: Configuration for RLE loss
        """
        super(MPPET_RLE, self).__init__()
        
        self.joint_training = joint_training
        self.pose_loss_weight = pose_loss_weight
        self.detection_loss_weight = detection_loss_weight
        
        # Default configurations
        if pose_config is None:
            pose_config = self._get_default_pose_config()
        if detection_config is None:
            detection_config = self._get_default_detection_config()
        if tracking_config is None:
            tracking_config = self._get_default_tracking_config()
        if rle_loss_config is None:
            rle_loss_config = self._get_default_rle_loss_config()
        
        # Person detector - Use lightweight YOLOv5 for faster inference
        from detection.detector import build_detector
        detector_type = detection_config.get('detector_type', 'yolov5')
        
        # Prepare config for build_detector function
        detector_config = detection_config.copy()
        if 'detector_type' in detector_config:
            detector_config['type'] = detector_config.pop('detector_type')
        else:
            detector_config['type'] = detector_type
        
        self.detector = build_detector(detector_config)
        
        # RLE pose estimator
        self.pose_estimator = SimpleRegressFlow(
            num_joints=pose_config.get('num_joints', 17),
            input_size=pose_config.get('input_size', (192, 256)),
            normalize_coords=pose_config.get('normalize_coords', False),  # Handle normalization manually
            backbone=pose_config.get('backbone', 'resnet50'),
            pretrained=pose_config.get('pretrained', True)
        )
        
        # RLE Loss for pose estimation
        self.rle_loss = RLELoss(**rle_loss_config)
        
        # Tracker (used only during inference)
        self.tracker = MPPETTracker(**tracking_config)
        
        # Configuration
        self.crop_size = pose_config.get('input_size', (192, 256))  # (width, height)
        self.min_crop_size = 32  # Minimum crop size to avoid too small crops
        self.crop_padding = 0.1  # Padding ratio for crops
        
    def _get_default_pose_config(self):
        """Get default pose estimation configuration."""
        return {
            'num_joints': 17,
            'input_size': (192, 256),
            'backbone': 'resnet50',
            'pretrained': True,
            'normalize_coords': False
        }
    
    def _get_default_detection_config(self):
        """Get default detection configuration for YOLOv5."""
        return {
            'detector_type': 'YOLOv5Detector',  # Use YOLOv5 for faster inference
            'model_name': 'yolov5s',    # Lightweight variant
            'score_threshold': 0.5,
            'nms_threshold': 0.5,
            'device': 'cuda'
        }
    
    def _get_default_tracking_config(self):
        """Get default tracking configuration."""
        return {
            'association_type': 'distribution_aware',
            'motion_model': 'kalman',
            'max_age': 30,
            'min_hits': 3,
            'bbox_weight': 0.3,
            'pose_weight': 0.7,
            'max_distance': 100.0,
            'uncertainty_threshold': 10.0
        }
    
    def _get_default_rle_loss_config(self):
        """Get default RLE loss configuration."""
        return {
            'use_target_weight': True,
            'size_average': True,
            'residual_normalizer': 1.0,
            'q_dis': 'laplace'
        }
    
    def forward(self, images, targets=None, mode='train'):
        """
        Forward pass of MPPET-RLE.
        
        Args:
            images: Input images tensor (B, C, H, W) or list of images
            targets: Ground truth targets for training
            mode: 'train', 'detect_pose', or 'track'
            
        Returns:
            If training: dictionary with losses
            If 'detect_pose': list of detection results with poses and uncertainties
            If 'track': list of tracking results
        """
        if mode == 'train' and targets is not None:
            return self._forward_train(images, targets)
        elif mode == 'detect_pose':
            return self._forward_detect_pose(images)
        elif mode == 'track':
            return self._forward_track(images)
        else:
            raise ValueError(f"Unknown mode: {mode}. Supported modes: 'train', 'detect_pose', 'track'")
    
    def _forward_train(self, images, targets):
        """
        End-to-end training forward pass with joint detection and pose estimation.
        
        Args:
            images: Input images (B, C, H, W) or list of images
            targets: List of target dictionaries containing:
                    - boxes: GT bounding boxes
                    - labels: GT labels (for detection)
                    - keypoints: GT keypoints (for pose estimation)
                    
        Returns:
            Dictionary containing combined losses
        """
        losses = {}
        
        # Convert to list format if tensor
        if isinstance(images, torch.Tensor):
            images = [images[i] for i in range(images.size(0))]
        
        # 1. Detection Loss (if joint training enabled)
        if self.joint_training and self.detection_loss_weight > 0:
            try:
                # Prepare detection targets
                detection_targets = []
                for target in targets:
                    det_target = {
                        'boxes': target.get('boxes', torch.empty((0, 4))),
                        'labels': target.get('labels', torch.empty((0,), dtype=torch.long))
                    }
                    detection_targets.append(det_target)
                
                # Forward through detector (training mode)
                self.detector.train()
                detection_losses = self.detector(images, detection_targets)
                
                # Add detection losses
                for key, value in detection_losses.items():
                    losses[f'det_{key}'] = value * self.detection_loss_weight
                    
            except Exception as e:
                print(f"Warning: Detection loss computation failed: {e}")
                # Add dummy detection loss to prevent training issues
                losses['det_loss_classifier'] = torch.tensor(0.0, device=images[0].device, requires_grad=True)
        
        # 2. Pose Estimation Loss
        if self.pose_loss_weight > 0:
            pose_losses = self._compute_pose_loss(images, targets)
            for key, value in pose_losses.items():
                losses[f'pose_{key}'] = value * self.pose_loss_weight
        
        return losses
    
    def _compute_pose_loss(self, images, targets):
        """
        Compute pose estimation loss using RLE.
        
        Args:
            images: List of input images
            targets: List of target dictionaries
            
        Returns:
            Dictionary containing pose losses
        """
        all_crops = []
        all_target_uv = []
        all_target_weights = []
        
        for img_idx, image in enumerate(images):
            target = targets[img_idx]
            
            # Extract crops and targets from GT bounding boxes
            crops, pose_targets = self._extract_pose_crops_from_gt(image, target)
            
            if crops is not None and len(crops) > 0:
                all_crops.extend(crops)
                all_target_uv.extend(pose_targets['target_uv'])
                all_target_weights.extend(pose_targets['target_weights'])
        
        if len(all_crops) == 0:
            # Return dummy loss if no valid crops
            return {'rle_loss': torch.tensor(0.0, device=images[0].device, requires_grad=True)}
        
        # Stack into batches
        crops_batch = torch.stack(all_crops)
        target_uv_batch = torch.stack(all_target_uv)
        target_weights_batch = torch.stack(all_target_weights)
        
        # Forward through pose estimator
        self.pose_estimator.train()
        pose_output = self.pose_estimator(crops_batch)
        
        # Compute RLE loss
        rle_loss = self.rle_loss(
            output=pose_output.pred_jts,
            target=target_uv_batch,
            target_weight=target_weights_batch,
            sigma=pose_output.sigma
        )
        
        return {'rle_loss': rle_loss}
    
    def _forward_detect_pose(self, images):
        """
        Integrated detection and pose estimation for inference.
        
        Args:
            images: Input images (B, C, H, W) or list of images
            
        Returns:
            List of detection results with poses and uncertainties for each image
        """
        # Convert to list format if tensor
        if isinstance(images, torch.Tensor):
            images = [images[i] for i in range(images.size(0))]
        
        # Set models to eval mode
        self.detector.eval()
        self.pose_estimator.eval()
        
        results = []
        
        with torch.no_grad():
            # Get detections from detector
            raw_detections = self.detector(images)
            
            for img_idx, image in enumerate(images):
                img_detections = raw_detections[img_idx]
                
                if len(img_detections['boxes']) == 0:
                    results.append([])
                    continue
                
                # Extract crops from detected bounding boxes
                crops, crop_info = self._extract_crops_from_detections(image, img_detections)
                
                if crops is None or len(crops) == 0:
                    results.append([])
                    continue
                
                # Run pose estimation on crops
                pose_output = self.pose_estimator(crops)
                
                # Convert poses back to image coordinates
                img_results = self._convert_poses_to_image_coords(
                    pose_output, crop_info, img_detections
                )
                
                results.append(img_results)
        
        return results
    
    def _forward_track(self, images):
        """
        Tracking forward pass using integrated detection and pose estimation.
        
        Args:
            images: Input images (B, C, H, W) or list of images
            
        Returns:
            List of tracking results for each image
        """
        # Get detections with poses and uncertainties
        detection_pose_results = self._forward_detect_pose(images)
        
        tracking_results = []
        
        for img_detections in detection_pose_results:
            # Convert to format expected by tracker
            tracker_detections = []
            for det in img_detections:
                tracker_det = {
                    'bbox': det['bbox'],
                    'pose_mu': det['pose_mu'],
                    'pose_sigma': det['pose_sigma'],  # RLE uncertainty estimates
                    'score': det['score'],
                    'pose_confidence': det.get('pose_confidence', 1.0)
                }
                tracker_detections.append(tracker_det)
            
            # Run tracker with uncertainty-aware association
            tracks = self.tracker(tracker_detections)
            tracking_results.append(tracks)
        
        return tracking_results
    
    def _extract_pose_crops_from_gt(self, image, target):
        """
        Extract person crops from ground truth bounding boxes for training.
        
        Args:
            image: Input image (C, H, W)
            target: Target dictionary containing 'boxes' and 'keypoints'
            
        Returns:
            Tuple of (crops, pose_targets) where:
            - crops: List of cropped image tensors
            - pose_targets: Dictionary with 'target_uv' and 'target_weights'
        """
        if 'boxes' not in target or 'keypoints' not in target:
            return None, None
        
        gt_boxes = target['boxes']
        gt_keypoints = target['keypoints']
        
        if len(gt_boxes) == 0:
            return None, None
        
        crops = []
        target_uv_list = []
        target_weights_list = []
        
        for i in range(len(gt_boxes)):
            bbox = gt_boxes[i]
            keypoints = gt_keypoints[i]  # Shape: (num_joints, 3) [x, y, visibility]
            
            # Extract crop with padding
            crop, crop_coords = self._extract_single_crop(image, bbox)
            if crop is None:
                continue
            
            # Transform keypoints to crop coordinates
            target_uv, target_weights = self._transform_keypoints_to_crop(
                keypoints, crop_coords, self.crop_size
            )
            
            crops.append(crop)
            target_uv_list.append(target_uv)
            target_weights_list.append(target_weights)
        
        if len(crops) == 0:
            return None, None
        
        pose_targets = {
            'target_uv': target_uv_list,
            'target_weights': target_weights_list
        }
        
        return crops, pose_targets
    
    def _extract_single_crop(self, image, bbox):
        """
        Extract a single crop from image using bounding box.
        
        Args:
            image: Input image (C, H, W)
            bbox: Bounding box [x1, y1, x2, y2]
            
        Returns:
            Tuple of (crop, crop_coords) where crop_coords = [x1, y1, x2, y2]
        """
        x1, y1, x2, y2 = bbox.float()
        
        # Add padding
        width = x2 - x1
        height = y2 - y1
        pad_x = width * self.crop_padding
        pad_y = height * self.crop_padding
        
        # Apply padding and clamp to image boundaries
        x1_pad = max(0, x1 - pad_x)
        y1_pad = max(0, y1 - pad_y)
        x2_pad = min(image.shape[2], x2 + pad_x)
        y2_pad = min(image.shape[1], y2 + pad_y)
        
        # Check minimum crop size
        if (x2_pad - x1_pad) < self.min_crop_size or (y2_pad - y1_pad) < self.min_crop_size:
            return None, None
        
        # Extract crop
        crop = image[:, int(y1_pad):int(y2_pad), int(x1_pad):int(x2_pad)]
        
        # Resize to standard size
        crop_resized = F.interpolate(
            crop.unsqueeze(0),
            size=self.crop_size[::-1],  # (height, width)
            mode='bilinear',
            align_corners=False
        ).squeeze(0)
        
        crop_coords = [x1_pad, y1_pad, x2_pad, y2_pad]
        return crop_resized, crop_coords
    
    def _transform_keypoints_to_crop(self, keypoints, crop_coords, crop_size):
        """
        Transform keypoints from image coordinates to crop coordinates.
        
        Args:
            keypoints: Keypoints tensor (num_joints, 3) [x, y, visibility]
            crop_coords: Crop coordinates [x1, y1, x2, y2]
            crop_size: Target crop size (width, height)
            
        Returns:
            Tuple of (target_uv, target_weights)
        """
        x1, y1, x2, y2 = crop_coords
        crop_w, crop_h = crop_size
        
        num_joints = keypoints.shape[0]
        target_uv = torch.zeros((num_joints, 2), dtype=torch.float32, device=keypoints.device)
        target_weights = torch.zeros((num_joints, 1), dtype=torch.float32, device=keypoints.device)
        
        for j in range(num_joints):
            x, y, vis = keypoints[j]
            
            if vis > 0:  # If keypoint is visible
                # Transform to crop coordinates
                crop_x = (x - x1) / (x2 - x1) * crop_w
                crop_y = (y - y1) / (y2 - y1) * crop_h
                
                # Check if keypoint is within crop bounds
                if 0 <= crop_x < crop_w and 0 <= crop_y < crop_h:
                    target_uv[j, 0] = crop_x
                    target_uv[j, 1] = crop_y
                    target_weights[j, 0] = 1.0
        
        return target_uv, target_weights
    
    def _extract_crops_from_detections(self, image, detections):
        """
        Extract crops from detected bounding boxes for inference.
        
        Args:
            image: Input image (C, H, W)
            detections: Detection results with 'boxes' and 'scores'
            
        Returns:
            Tuple of (crops_batch, crop_info)
        """
        boxes = detections['boxes']
        scores = detections['scores']
        
        if len(boxes) == 0:
            return None, []
        
        crops = []
        crop_info = []
        
        for i, bbox in enumerate(boxes):
            crop, crop_coords = self._extract_single_crop(image, bbox)
            if crop is not None:
                crops.append(crop)
                crop_info.append({
                    'original_bbox': bbox,
                    'crop_coords': crop_coords,
                    'score': scores[i]
                })
        
        if len(crops) == 0:
            return None, []
        
        return torch.stack(crops), crop_info
    
    def _convert_poses_to_image_coords(self, pose_output, crop_info, detections):
        """
        Convert pose predictions from crop coordinates to image coordinates.
        
        Args:
            pose_output: Pose estimation output with pred_jts and sigma
            crop_info: Information about crops and coordinates
            detections: Original detection results
            
        Returns:
            List of detection results with poses and uncertainties
        """
        pred_poses = pose_output.pred_jts  # (N, num_joints, 2)
        pred_sigmas = pose_output.sigma    # (N, num_joints, 2)
        
        results = []
        
        for i in range(len(pred_poses)):
            crop_coords = crop_info[i]['crop_coords']
            original_bbox = crop_info[i]['original_bbox']
            score = crop_info[i]['score']
            
            # Transform pose to image coordinates
            pose_mu_img, pose_sigma_img = self._transform_pose_to_image_coords(
                pred_poses[i], pred_sigmas[i], crop_coords, self.crop_size
            )
            
            # Compute pose confidence based on uncertainty
            pose_confidence = self._compute_pose_confidence(pred_sigmas[i])
            
            result = {
                'bbox': original_bbox.cpu().numpy(),
                'score': score.cpu().numpy(),
                'pose_mu': pose_mu_img,
                'pose_sigma': pose_sigma_img,
                'pose_confidence': pose_confidence
            }
            results.append(result)
        
        return results
    
    def _transform_pose_to_image_coords(self, pose_mu, pose_sigma, crop_coords, crop_size):
        """
        Transform pose from crop coordinates to image coordinates.
        
        Args:
            pose_mu: Pose coordinates in crop space (num_joints, 2)
            pose_sigma: Pose uncertainties in crop space (num_joints, 2)
            crop_coords: Crop coordinates [x1, y1, x2, y2]
            crop_size: Crop size (width, height)
            
        Returns:
            Tuple of (pose_mu_img, pose_sigma_img) in image coordinates
        """
        x1, y1, x2, y2 = crop_coords
        crop_w, crop_h = crop_size
        
        # Scale factors
        scale_x = float((x2 - x1) / crop_w)
        scale_y = float((y2 - y1) / crop_h)
        
        # Transform pose coordinates
        pose_mu_np = pose_mu.cpu().numpy()
        pose_mu_img = pose_mu_np.copy()
        pose_mu_img[:, 0] = pose_mu_np[:, 0] * scale_x + float(x1)
        pose_mu_img[:, 1] = pose_mu_np[:, 1] * scale_y + float(y1)
        
        # Transform uncertainties
        pose_sigma_np = pose_sigma.cpu().numpy()
        pose_sigma_img = pose_sigma_np.copy()
        pose_sigma_img[:, 0] = pose_sigma_np[:, 0] * scale_x
        pose_sigma_img[:, 1] = pose_sigma_np[:, 1] * scale_y
        
        return pose_mu_img, pose_sigma_img
    
    def _compute_pose_confidence(self, pose_sigma):
        """
        Compute overall pose confidence from uncertainties.
        
        Args:
            pose_sigma: Pose uncertainties (num_joints, 2)
            
        Returns:
            Pose confidence score
        """
        # Use inverse of mean uncertainty as confidence
        mean_uncertainty = torch.mean(pose_sigma)
        confidence = 1.0 / (1.0 + mean_uncertainty.cpu().numpy())
        return float(confidence)
    
    def reset_tracker(self):
        """Reset tracker state."""
        self.tracker.reset()
    
    def set_mode(self, mode):
        """Set model mode."""
        if mode == 'train':
            self.train()
        else:
            self.eval()
    
    def get_model_info(self):
        """Get model information for debugging."""
        info = {
            'joint_training': self.joint_training,
            'pose_loss_weight': self.pose_loss_weight,
            'detection_loss_weight': self.detection_loss_weight,
            'crop_size': self.crop_size,
            'num_parameters': sum(p.numel() for p in self.parameters()),
            'num_trainable_parameters': sum(p.numel() for p in self.parameters() if p.requires_grad)
        }
        return info


def build_mppet_rle(cfg):
    """Build MPPET-RLE model from configuration."""
    return MPPET_RLE(
        pose_config=cfg.get('pose_config'),
        detection_config=cfg.get('detection_config'),
        tracking_config=cfg.get('tracking_config'),
        joint_training=cfg.get('joint_training', True),
        pose_loss_weight=cfg.get('pose_loss_weight', 1.0),
        detection_loss_weight=cfg.get('detection_loss_weight', 1.0),
        rle_loss_config=cfg.get('rle_loss_config')
    )
