"""
RLE (Residual Log-likelihood Estimation) Loss Functions
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class RLELoss(nn.Module):
    """
    Residual Log-likelihood Estimation Loss for pose estimation with uncertainty.
    
    This loss function combines:
    1. L1 loss for pose coordinates
    2. Negative log-likelihood estimation for uncertainty
    """
    
    def __init__(self, 
                 use_target_weight=True,
                 size_average=True,
                 residual_normalizer=1.0,
                 q_dis='laplace'):
        """
        Initialize RLE Loss.
        
        Args:
            use_target_weight: Whether to use target weights
            size_average: Whether to average the loss
            residual_normalizer: Normalizer for residuals
            q_dis: Distribution type ('laplace' or 'gaussian')
        """
        super(RLELoss, self).__init__()
        
        self.use_target_weight = use_target_weight
        self.size_average = size_average
        self.residual_normalizer = residual_normalizer
        self.q_dis = q_dis
        
        if q_dis == 'laplace':
            self.log_q = self.log_q_laplace
        elif q_dis == 'gaussian':
            self.log_q = self.log_q_gaussian
        else:
            raise ValueError(f"Unknown distribution: {q_dis}")
    
    def log_q_laplace(self, target, pred, sigma):
        """
        Compute log probability under Laplace distribution.
        
        Args:
            target: Ground truth coordinates (B, N, 2)
            pred: Predicted coordinates (B, N, 2)
            sigma: Predicted standard deviation (B, N, 2)
            
        Returns:
            Log probability
        """
        return -torch.log(sigma * 2) - torch.abs(target - pred) / sigma
    
    def log_q_gaussian(self, target, pred, sigma):
        """
        Compute log probability under Gaussian distribution.
        
        Args:
            target: Ground truth coordinates (B, N, 2)
            pred: Predicted coordinates (B, N, 2)
            sigma: Predicted standard deviation (B, N, 2)
            
        Returns:
            Log probability
        """
        return -torch.log(sigma * np.sqrt(2 * np.pi)) - 0.5 * torch.pow((target - pred) / sigma, 2)
    
    def forward(self, output, target, target_weight=None, sigma=None):
        """
        Compute RLE loss.
        
        Args:
            output: Predicted coordinates (B, N, 2) or model output dict/EasyDict
            target: Ground truth coordinates (B, N, 2) or target dict
            target_weight: Weight for each joint (B, N, 1) or (B, N, 2)
            sigma: Predicted uncertainties (B, N, 2), if not in output
            
        Returns:
            Loss value
        """
        from easydict import EasyDict
        
        # Handle different input formats
        if isinstance(output, (dict, EasyDict)):
            pred_jts = output.pred_jts if hasattr(output, 'pred_jts') else output['pred_jts']
            pred_sigma = output.sigma if hasattr(output, 'sigma') else output['sigma']
        else:
            pred_jts = output
            pred_sigma = sigma
            
        if isinstance(target, dict):
            target_uv = target['target_uv']
            if target_weight is None:
                target_weight = target.get('target_weights', target.get('target_uv_weight'))
        else:
            target_uv = target
        
        # Default target weight if not provided
        if target_weight is None:
            target_weight = torch.ones_like(target_uv)
        
        # Ensure target_weight has correct shape
        if target_weight.dim() == 3 and target_weight.size(-1) == 1:
            target_weight = target_weight.expand(-1, -1, 2)
        
        # Ensure sigma is positive
        pred_sigma = torch.clamp(pred_sigma, min=1e-6)
        
        # Compute residuals
        residual = torch.abs(target_uv - pred_jts) / self.residual_normalizer
        
        # Compute log probability
        log_prob = self.log_q(target_uv, pred_jts, pred_sigma)
        
        # Apply target weights if specified
        if self.use_target_weight and target_weight is not None:
            log_prob = log_prob * target_weight
            residual = residual * target_weight
        
        # RLE loss: negative log-likelihood + residual term  
        rle_loss = -log_prob + residual
        
        # Compute final loss
        if self.size_average:
            if self.use_target_weight and target_weight is not None:
                # Average only over valid joints
                valid_joints = target_weight.sum()
                if valid_joints > 0:
                    rle_loss = rle_loss.sum() / valid_joints
                else:
                    rle_loss = rle_loss.sum() * 0  # Return 0 if no valid joints
            else:
                rle_loss = rle_loss.mean()
        else:
            rle_loss = rle_loss.sum()
        
        return rle_loss


class AdaptiveRLELoss(nn.Module):
    """
    Adaptive RLE Loss with learnable parameters.
    """
    
    def __init__(self,
                 use_target_weight=True,
                 size_average=True,
                 residual_normalizer=1.0,
                 q_dis='laplace',
                 adaptive_weight=True):
        """
        Initialize Adaptive RLE Loss.
        
        Args:
            use_target_weight: Whether to use target weights
            size_average: Whether to average the loss
            residual_normalizer: Normalizer for residuals
            q_dis: Distribution type
            adaptive_weight: Whether to use adaptive weighting
        """
        super(AdaptiveRLELoss, self).__init__()
        
        self.base_loss = RLELoss(
            use_target_weight=use_target_weight,
            size_average=size_average,
            residual_normalizer=residual_normalizer,
            q_dis=q_dis
        )
        
        self.adaptive_weight = adaptive_weight
        
        if adaptive_weight:
            # Learnable weights for different loss components
            self.coord_weight = nn.Parameter(torch.ones(1))
            self.uncertainty_weight = nn.Parameter(torch.ones(1))
    
    def forward(self, output, target):
        """
        Compute adaptive RLE loss.
        
        Args:
            output: Model output
            target: Ground truth target
            
        Returns:
            Loss value
        """
        rle_loss = self.base_loss(output, target)
        
        if self.adaptive_weight:
            # Extract sigma for uncertainty regularization
            from easydict import EasyDict
            
            if isinstance(output, (dict, EasyDict)):
                sigma = output.sigma if hasattr(output, 'sigma') else output['sigma']
            else:
                # If output is just pose predictions, no uncertainty regularization
                return self.coord_weight * rle_loss
            
            # Apply learnable weights
            uncertainty_reg = torch.mean(sigma)
            weighted_loss = (
                self.coord_weight * rle_loss +
                self.uncertainty_weight * uncertainty_reg
            )
            return weighted_loss
        else:
            return rle_loss


class MultiPersonRLELoss(nn.Module):
    """
    RLE Loss for multi-person pose estimation.
    """
    
    def __init__(self, 
                 use_target_weight=True,
                 size_average=True,
                 person_weight=True):
        """
        Initialize multi-person RLE loss.
        
        Args:
            use_target_weight: Whether to use target weights
            size_average: Whether to average the loss
            person_weight: Whether to weight by number of persons
        """
        super(MultiPersonRLELoss, self).__init__()
        
        self.base_loss = RLELoss(
            use_target_weight=use_target_weight,
            size_average=size_average
        )
        self.person_weight = person_weight
    
    def forward(self, outputs, targets):
        """
        Compute multi-person RLE loss.
        
        Args:
            outputs: List of outputs for each person
            targets: List of targets for each person
            
        Returns:
            Average loss across all persons
        """
        if len(outputs) == 0:
            return torch.tensor(0.0, requires_grad=True)
        
        total_loss = 0.0
        valid_persons = 0
        
        for output, target in zip(outputs, targets):
            if target is not None and 'target_uv' in target:
                person_loss = self.base_loss(output, target)
                total_loss += person_loss
                valid_persons += 1
        
        if valid_persons > 0:
            if self.person_weight:
                return total_loss / valid_persons
            else:
                return total_loss
        else:
            return torch.tensor(0.0, requires_grad=True)
