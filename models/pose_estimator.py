"""
Simple RLE-based pose estimation model
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from easydict import EasyDict

from layers.Resnet import ResNet, BasicBlock, Bottleneck


class SimpleRegressFlow(nn.Module):
    """
    Simple RLE-based pose estimation model with normalizing flow for uncertainty estimation.
    
    This model predicts both pose coordinates and their uncertainties using
    Residual Log-likelihood Estimation (RLE).
    """
    
    def __init__(self, 
                 num_joints=17,
                 input_size=(192, 256),
                 normalize_coords=True,
                 backbone='resnet50',
                 pretrained=True,
                 flow_depth=6,
                 flow_hidden_dim=256):
        """
        Initialize SimpleRegressFlow model.
        
        Args:
            num_joints: Number of pose keypoints
            input_size: Input image size (width, height)
            normalize_coords: Whether to normalize coordinates to [0, 1]
            backbone: Backbone architecture
            pretrained: Use pretrained weights
            flow_depth: Depth of normalizing flow
            flow_hidden_dim: Hidden dimension of flow
        """
        super(SimpleRegressFlow, self).__init__()
        
        self.num_joints = num_joints
        self.input_size = input_size
        self.normalize_coords = normalize_coords
        
        # Backbone network
        if backbone == 'resnet50':
            self.backbone = ResNet(architecture='resnet50', pretrained=pretrained)
            backbone_dim = 2048
        elif backbone == 'resnet34':
            self.backbone = ResNet(architecture='resnet34', pretrained=pretrained)
            backbone_dim = 512
        else:
            raise ValueError(f"Unsupported backbone: {backbone}")
        
        # Average pooling
        self.avgpool = nn.AdaptiveAvgPool2d((1, 1))
        
        # Pose regression head
        self.pose_head = nn.Sequential(
            nn.Linear(backbone_dim, 1024),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(1024, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, num_joints * 2)  # x, y coordinates
        )
        
        # Uncertainty estimation head (log variance)
        self.sigma_head = nn.Sequential(
            nn.Linear(backbone_dim, 512),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(inplace=True),
            nn.Dropout(0.5),
            nn.Linear(256, num_joints * 2)  # log variance for x, y
        )
        
        # Initialize weights
        self._init_weights()
        
    def _init_weights(self):
        """Initialize network weights."""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                nn.init.normal_(m.weight, 0, 0.01)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def forward(self, x, targets=None):
        """
        Forward pass.
        
        Args:
            x: Input images (B, 3, H, W)
            targets: Ground truth targets for training
            
        Returns:
            EasyDict with predictions
        """
        batch_size = x.size(0)
        
        # Extract features
        features = self.backbone(x)
        
        # Global average pooling
        pooled_features = self.avgpool(features)
        pooled_features = pooled_features.view(batch_size, -1)
        
        # Predict pose coordinates
        pred_coords = self.pose_head(pooled_features)
        pred_coords = pred_coords.view(batch_size, self.num_joints, 2)
        
        # Predict uncertainties (log variance)
        log_sigma = self.sigma_head(pooled_features)
        log_sigma = log_sigma.view(batch_size, self.num_joints, 2)
        
        # Convert to standard deviation
        sigma = torch.exp(log_sigma / 2)
        
        # Normalize coordinates if requested
        if self.normalize_coords:
            pred_coords[:, :, 0] = pred_coords[:, :, 0] / self.input_size[0]  # x
            pred_coords[:, :, 1] = pred_coords[:, :, 1] / self.input_size[1]  # y
        
        # Create output dictionary
        output = EasyDict()
        output.pred_jts = pred_coords
        output.sigma = sigma
        output.log_sigma = log_sigma
        
        # Add targets if provided (for loss computation)
        if targets is not None:
            output.targets = targets
        
        return output
    
    def predict_with_uncertainty(self, x):
        """
        Predict poses with uncertainty estimates.
        
        Args:
            x: Input images (B, 3, H, W)
            
        Returns:
            Tuple of (poses, uncertainties)
        """
        self.eval()
        with torch.no_grad():
            output = self.forward(x)
            return output.pred_jts, output.sigma
