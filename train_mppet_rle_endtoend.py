#!/usr/bin/env python3
"""
End-to-End Training Script for MPPET-RLE
Multi-Task Learning with Detection and Pose Estimation
"""

import os
import sys
import argparse
import logging
import time
from datetime import datetime
import json

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.optim.lr_scheduler import MultiStepLR, ReduceLROnPlateau
import numpy as np

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from configs.posetrack21_config import posetrack21_config as base_config
from configs.mppet_rle_endtoend_config import get_config as get_rle_config
from datasets.posetrack_dataset_rle import PoseTrackDatasetRLE
from models.mppet_rle import build_mppet_rle
from models.losses import RLELoss
from utils.metrics import PoseTrackingMetrics
from utils.posetrack21_metrics import PoseTrack21Evaluator
from utils.visualization import visualize_batch_predictions
from builder import build_optimizer, build_lr_scheduler


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Train MPPET-RLE End-to-End')
    parser.add_argument('--data_root', type=str, default='data/demo',
                       help='Root directory for data')
    parser.add_argument('--batch_size', type=int, default=4,
                       help='Training batch size')
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--lr', type=float, default=1e-4,
                       help='Learning rate')
    parser.add_argument('--save_dir', type=str, default='checkpoints',
                       help='Directory to save checkpoints')
    parser.add_argument('--log_dir', type=str, default='logs',
                       help='Directory for logs')
    parser.add_argument('--use_dummy_data', action='store_true',
                       help='Use dummy data for testing')
    parser.add_argument('--resume', type=str, default='',
                       help='Resume from checkpoint')
    parser.add_argument('--eval-only', action='store_true',
                       help='Only run evaluation')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode')
    parser.add_argument('--workers', type=int, default=4,
                       help='Number of data loading workers')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--mixed-precision', action='store_true',
                       help='Enable mixed precision training')
    
    return parser.parse_args()


def setup_logging(log_dir, experiment_name):
    """Setup logging configuration."""
    os.makedirs(log_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f'{experiment_name}_{timestamp}.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    return logging.getLogger(__name__)


def setup_device(device_arg):
    """Setup computing device."""
    if device_arg == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_arg)
    
    if device.type == 'cuda':
        torch.backends.cudnn.benchmark = True
        print(f"Using GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(device).total_memory / 1e9:.1f} GB")
    
    return device


def build_datasets(args):
    """Build training and validation datasets."""
    if args.use_dummy_data:
        # Use dummy dataset for testing
        from datasets.posetrack_dataset_rle import DummyPoseTrackDatasetRLE
        
        train_dataset = DummyPoseTrackDatasetRLE(
            root_dir=args.data_root,
            num_samples=100,
            image_size=(256, 192),
            num_joints=17
        )
        
        val_dataset = DummyPoseTrackDatasetRLE(
            root_dir=args.data_root,
            num_samples=20,
            image_size=(256, 192),
            num_joints=17
        )
    else:
        # Real dataset - Use PoseTrack21 data
        dataset_root = "/home/<USER>/workspace/pktrack/datasets/PoseTrack21/data"
        
        train_dataset = PoseTrackDatasetRLE(
            root_dir=os.path.join(dataset_root, 'images'),
            ann_file=os.path.join(dataset_root, 'posetrack_data'),
            image_size=(256, 192),
            num_joints=17,
            subset_ratio=0.1  # Use 10% for faster training
        )
        
        val_dataset = PoseTrackDatasetRLE(
            root_dir=os.path.join(dataset_root, 'images'),
            ann_file=os.path.join(dataset_root, 'posetrack_data'),
            image_size=(256, 192),
            num_joints=17,
            subset_ratio=0.05  # Use 5% for validation
        )
    
    return train_dataset, val_dataset


def build_data_loaders(train_dataset, val_dataset, batch_size, num_workers):
    """Build data loaders."""
    # Custom collate function for variable number of detections
    def collate_fn(batch):
        """Custom collate function to handle variable-length targets."""
        images = []
        targets = []
        
        for sample in batch:
            images.append(sample['image'])
            targets.append(sample['target'])
        
        return {
            'images': images,  # List of tensors
            'targets': targets  # List of target dicts
        }
    
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=True,
        drop_last=True,
        collate_fn=collate_fn
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=True,
        collate_fn=collate_fn
    )
    
    return train_loader, val_loader


def save_checkpoint(model, optimizer, lr_scheduler, epoch, best_metric, 
                   checkpoint_dir, filename):
    """Save model checkpoint."""
    os.makedirs(checkpoint_dir, exist_ok=True)
    
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'lr_scheduler_state_dict': lr_scheduler.state_dict() if lr_scheduler else None,
        'best_metric': best_metric,
        'model_config': model.get_model_info()
    }
    
    filepath = os.path.join(checkpoint_dir, filename)
    torch.save(checkpoint, filepath)
    print(f"Checkpoint saved: {filepath}")


def load_checkpoint(model, optimizer, lr_scheduler, checkpoint_path, device):
    """Load model checkpoint."""
    print(f"Loading checkpoint: {checkpoint_path}")
    
    checkpoint = torch.load(checkpoint_path, map_location=device)
    model.load_state_dict(checkpoint['model_state_dict'])
    
    if optimizer and 'optimizer_state_dict' in checkpoint:
        optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    
    if lr_scheduler and 'lr_scheduler_state_dict' in checkpoint:
        lr_scheduler.load_state_dict(checkpoint['lr_scheduler_state_dict'])
    
    start_epoch = checkpoint.get('epoch', 0) + 1
    best_metric = checkpoint.get('best_metric', 0.0)
    
    print(f"Resumed from epoch {start_epoch}, best metric: {best_metric:.4f}")
    return start_epoch, best_metric


def train_epoch(model, train_loader, optimizer, device, epoch, logger, 
                mixed_precision=False):
    """Train for one epoch."""
    model.train()
    
    total_loss = 0.0
    det_loss_sum = 0.0
    pose_loss_sum = 0.0
    num_batches = 0
    
    # Mixed precision training
    scaler = torch.amp.GradScaler('cuda') if mixed_precision else None
    
    for batch_idx, batch in enumerate(train_loader):
        images = batch['images']
        targets = batch['targets']
        
        # Move to device
        images = [img.to(device) for img in images]
        targets = [{k: v.to(device) if isinstance(v, torch.Tensor) else v 
                   for k, v in target.items()} for target in targets]
        
        optimizer.zero_grad()
        
        # Forward pass
        if mixed_precision:
            with torch.amp.autocast('cuda'):
                losses = model(images, targets, mode='train')
        else:
            losses = model(images, targets, mode='train')
        
        # Compute total loss
        total_batch_loss = sum(losses.values())
        
        # Backward pass
        if mixed_precision:
            scaler.scale(total_batch_loss).backward()
            scaler.step(optimizer)
            scaler.update()
        else:
            total_batch_loss.backward()
            optimizer.step()
        
        # Statistics
        total_loss += total_batch_loss.item()
        
        # Track individual losses
        for key, value in losses.items():
            if 'det_' in key:
                det_loss_sum += value.item()
            elif 'pose_' in key:
                pose_loss_sum += value.item()
        
        num_batches += 1
        
        # Log progress
        if batch_idx % 50 == 0:
            logger.info(f'Epoch {epoch} [{batch_idx}/{len(train_loader)}] '
                       f'Loss: {total_batch_loss.item():.4f} '
                       f'LR: {optimizer.param_groups[0]["lr"]:.6f}')
    
    # Average losses
    avg_total_loss = total_loss / num_batches
    avg_det_loss = det_loss_sum / num_batches
    avg_pose_loss = pose_loss_sum / num_batches
    
    logger.info(f'Epoch {epoch} Training - '
               f'Total Loss: {avg_total_loss:.4f}, '
               f'Det Loss: {avg_det_loss:.4f}, '
               f'Pose Loss: {avg_pose_loss:.4f}')
    
    return avg_total_loss


def validate_epoch(model, val_loader, device, epoch, logger, viz_dir=None):
    """Validate for one epoch."""
    model.eval()
    
    total_loss = 0.0
    det_loss_sum = 0.0
    pose_loss_sum = 0.0
    num_batches = 0
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for batch_idx, batch in enumerate(val_loader):
            images = batch['images']
            targets = batch['targets']
            
            # Move to device
            images = [img.to(device) for img in images]
            targets = [{k: v.to(device) if isinstance(v, torch.Tensor) else v 
                       for k, v in target.items()} for target in targets]
            
            # Compute losses (validation)
            losses = model(images, targets, mode='train')
            total_batch_loss = sum(losses.values())
            
            total_loss += total_batch_loss.item()
            
            # Track individual losses
            for key, value in losses.items():
                if 'det_' in key:
                    det_loss_sum += value.item()
                elif 'pose_' in key:
                    pose_loss_sum += value.item()
            
            # Get predictions for evaluation
            predictions = model(images, mode='detect_pose')
            
            all_predictions.extend(predictions)
            all_targets.extend(targets)
            
            num_batches += 1
            
            # Visualize first batch
            if batch_idx == 0 and viz_dir:
                try:
                    viz_epoch_dir = os.path.join(viz_dir, f'epoch_{epoch}')
                    os.makedirs(viz_epoch_dir, exist_ok=True)
                    
                    visualize_batch_predictions(
                        images, predictions, targets,
                        save_path=os.path.join(viz_epoch_dir, f'val_batch_{batch_idx}.png')
                    )
                except Exception as e:
                    logger.warning(f"Visualization failed: {e}")
    
    # Average losses
    avg_total_loss = total_loss / num_batches
    avg_det_loss = det_loss_sum / num_batches
    avg_pose_loss = pose_loss_sum / num_batches
    
    # Compute metrics
    try:
        metrics_calc = PoseTrackingMetrics(num_joints=17)
        
        for pred, target in zip(all_predictions, all_targets):
            # Extract poses and bboxes for metrics calculation
            if isinstance(pred, list) and len(pred) > 0:
                pred_poses = []
                pred_bboxes = []
                for p in pred:
                    if 'pose_mu' in p:
                        pred_poses.append(p['pose_mu'])
                    elif 'pose' in p:
                        pred_poses.append(p['pose'])
                    
                    if 'bbox' in p:
                        pred_bboxes.append(p['bbox'])
            else:
                pred_poses, pred_bboxes = [], []
            
            if 'annotations' in target:
                gt_poses = []
                gt_bboxes = []
                for ann in target['annotations']:
                    if 'keypoints' in ann:
                        kpts = np.array(ann['keypoints']).reshape(-1, 3)
                        gt_poses.append(kpts[:, :2])  # Only x, y coordinates
                    if 'bbox' in ann:
                        gt_bboxes.append(ann['bbox'])
            else:
                gt_poses, gt_bboxes = [], []
            
            if pred_poses and gt_poses:
                # Ensure consistent shapes
                min_poses = min(len(pred_poses), len(gt_poses))
                if min_poses > 0:
                    metrics_calc.update_pose_metrics(
                        gt_poses[:min_poses], 
                        pred_poses[:min_poses], 
                        gt_bboxes[:min_poses] if gt_bboxes else [[0,0,100,100]] * min_poses,
                        pred_bboxes[:min_poses] if pred_bboxes else [[0,0,100,100]] * min_poses
                    )
        
        pose_metrics = metrics_calc.get_pose_metrics()
        pck_score = pose_metrics.get('PCK', 0.0)
        
        # Additional metrics can be computed here
        # mota, motp = evaluate_posetrack21(all_predictions, all_targets)
        
    except Exception as e:
        logger.warning(f"Metric computation failed: {e}")
        pck_score = 0.0
    
    logger.info(f'Epoch {epoch} Validation - '
               f'Total Loss: {avg_total_loss:.4f}, '
               f'Det Loss: {avg_det_loss:.4f}, '
               f'Pose Loss: {avg_pose_loss:.4f}, '
               f'PCK: {pck_score:.4f}')
    
    return avg_total_loss, pck_score


def main():
    """Main training function."""
    args = parse_args()
    
    # Load configuration
    cfg = get_rle_config()  # Use RLE config as base
    
    # Override with command line arguments
    cfg.training.batch_size = args.batch_size
    cfg.training.num_epochs = args.epochs
    cfg.optimizer.lr = args.lr
    cfg.logging.log_dir = args.log_dir
    cfg.logging.checkpoint_dir = args.save_dir
    
    # Setup logging
    logger = setup_logging(args.log_dir, 'mppet_rle_endtoend')
    logger.info("Starting MPPET-RLE End-to-End Training")
    logger.info(f"Arguments: {args}")
    
    # Setup device
    device = setup_device(args.device)
    logger.info(f"Using device: {device}")
    
    # Build datasets and data loaders
    logger.info("Building datasets...")
    train_dataset, val_dataset = build_datasets(args)
    train_loader, val_loader = build_data_loaders(
        train_dataset, val_dataset, args.batch_size, args.workers
    )
    
    logger.info(f"Train dataset: {len(train_dataset)} samples")
    logger.info(f"Val dataset: {len(val_dataset)} samples")
    
    # Build model
    logger.info("Building model...")
    model = build_mppet_rle(cfg.model)
    model.to(device)
    
    logger.info(f"Model info: {model.get_model_info()}")
    
    # Build optimizer and scheduler
    optimizer = build_optimizer(cfg.optimizer, model.parameters())
    lr_scheduler = build_lr_scheduler(cfg.lr_scheduler, optimizer)
    
    # Resume from checkpoint if specified
    start_epoch = 0
    best_metric = 0.0
    
    if args.resume:
        start_epoch, best_metric = load_checkpoint(
            model, optimizer, lr_scheduler, args.resume, device
        )
    
    # Evaluation only mode
    if args.eval_only:
        logger.info("Running evaluation only...")
        val_loss, val_pck = validate_epoch(
            model, val_loader, device, 0, logger,
            viz_dir=os.path.join(args.log_dir, 'visualizations')
        )
        logger.info(f"Evaluation complete - Loss: {val_loss:.4f}, PCK: {val_pck:.4f}")
        return
    
    # Training loop
    logger.info("Starting training...")
    
    for epoch in range(start_epoch, args.epochs):
        epoch_start_time = time.time()
        
        # Training
        train_loss = train_epoch(
            model, train_loader, optimizer, device, epoch, logger,
            mixed_precision=args.mixed_precision
        )
        
        # Validation
        val_loss, val_pck = validate_epoch(
            model, val_loader, device, epoch, logger,
            viz_dir=os.path.join(args.log_dir, 'visualizations')
        )
        
        # Update learning rate
        if lr_scheduler:
            if isinstance(lr_scheduler, ReduceLROnPlateau):
                lr_scheduler.step(val_loss)
            else:
                lr_scheduler.step()
        
        # Save checkpoints
        is_best = val_pck > best_metric
        if is_best:
            best_metric = val_pck
        
        # Save regular checkpoint
        save_checkpoint(
            model, optimizer, lr_scheduler, epoch, best_metric,
            args.save_dir, f'checkpoint_epoch_{epoch}.pth'
        )
        
        # Save best checkpoint
        if is_best:
            save_checkpoint(
                model, optimizer, lr_scheduler, epoch, best_metric,
                args.save_dir, 'best_model.pth'
            )
        
        epoch_time = time.time() - epoch_start_time
        logger.info(f'Epoch {epoch} completed in {epoch_time:.1f}s')
        
        # Early stopping check
        if hasattr(cfg.training, 'early_stopping_patience'):
            # Implement early stopping logic here if needed
            pass
    
    logger.info("Training completed!")
    logger.info(f"Best validation PCK: {best_metric:.4f}")


if __name__ == '__main__':
    main()
