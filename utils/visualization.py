"""
Visualization utilities for MPPET-RLE
"""

import os
# Qt 환경 변수 설정 (OpenCV 오류 방지)
os.environ['QT_QPA_PLATFORM'] = 'offscreen'
import cv2
import numpy as np
import matplotlib
matplotlib.use('Agg')  # GUI가 필요없는 백엔드 사용
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Ellipse
import torch
import os


# PoseTrack21 skeleton connections
SKELETON_CONNECTIONS = [
    # Head
    (0, 1),   # nose -> head_bottom
    (1, 2),   # head_bottom -> head_top
    (0, 3),   # nose -> left_ear
    (0, 4),   # nose -> right_ear
    
    # Arms
    (5, 6),   # left_shoulder -> right_shoulder
    (5, 7),   # left_shoulder -> left_elbow
    (7, 9),   # left_elbow -> left_wrist
    (6, 8),   # right_shoulder -> right_elbow
    (8, 10),  # right_elbow -> right_wrist
    
    # Torso
    (5, 11),  # left_shoulder -> left_hip
    (6, 12),  # right_shoulder -> right_hip
    (11, 12), # left_hip -> right_hip
    
    # Legs
    (11, 13), # left_hip -> left_knee
    (13, 15), # left_knee -> left_ankle
    (12, 14), # right_hip -> right_knee
    (14, 16)  # right_knee -> right_ankle
]

# Colors for different tracks (BGR format for OpenCV)
TRACK_COLORS = [
    (255, 0, 0),    # Red
    (0, 255, 0),    # Green
    (0, 0, 255),    # Blue
    (255, 255, 0),  # Cyan
    (255, 0, 255),  # Magenta
    (0, 255, 255),  # Yellow
    (128, 0, 128),  # Purple
    (255, 165, 0),  # Orange
    (0, 128, 128),  # Teal
    (128, 128, 0),  # Olive
]


def draw_pose_with_uncertainty(image, pose_mu, pose_sigma, color=(0, 255, 0), thickness=2):
    """
    Draw pose keypoints with uncertainty visualization.
    
    Args:
        image: Input image (numpy array)
        pose_mu: Keypoint positions (num_joints, 2)
        pose_sigma: Keypoint uncertainties (num_joints, 2)
        color: Color for drawing (BGR)
        thickness: Line thickness
    
    Returns:
        Image with pose drawn
    """
    img = image.copy()
    
    # Convert to numpy if needed
    if torch.is_tensor(pose_mu):
        pose_mu = pose_mu.cpu().numpy()
    if torch.is_tensor(pose_sigma):
        pose_sigma = pose_sigma.cpu().numpy()
    
    # Draw skeleton connections
    for connection in SKELETON_CONNECTIONS:
        pt1_idx, pt2_idx = connection
        
        if pt1_idx < len(pose_mu) and pt2_idx < len(pose_mu):
            pt1 = tuple(pose_mu[pt1_idx].astype(int))
            pt2 = tuple(pose_mu[pt2_idx].astype(int))
            
            # Check if points are valid (within image bounds)
            if (0 <= pt1[0] < img.shape[1] and 0 <= pt1[1] < img.shape[0] and
                0 <= pt2[0] < img.shape[1] and 0 <= pt2[1] < img.shape[0]):
                cv2.line(img, pt1, pt2, color, thickness)
    
    # Draw keypoints with uncertainty
    for i, (point, sigma) in enumerate(zip(pose_mu, pose_sigma)):
        if isinstance(point, (list, tuple)):
            x, y = int(point[0]), int(point[1])
        else:
            x, y = point.astype(int)
        
        # Check if point is valid
        if 0 <= x < img.shape[1] and 0 <= y < img.shape[0]:
            # Draw keypoint
            cv2.circle(img, (x, y), 3, color, -1)
            
            # Draw uncertainty ellipse (simplified as circle)
            uncertainty_radius = int(np.mean(sigma) * 2)  # Scale factor for visualization
            uncertainty_radius = max(1, min(uncertainty_radius, 20))  # Clamp radius
            
            # Draw uncertainty circle with transparency effect
            overlay = img.copy()
            cv2.circle(overlay, (x, y), uncertainty_radius, color, 1)
            cv2.addWeighted(overlay, 0.3, img, 0.7, 0, img)
    
    return img


def draw_tracking_results(image, tracking_results, show_ids=True, show_uncertainty=True):
    """
    Draw tracking results on image.
    
    Args:
        image: Input image
        tracking_results: List of track results
        show_ids: Whether to show track IDs
        show_uncertainty: Whether to show pose uncertainty
    
    Returns:
        Image with tracking results drawn
    """
    img = image.copy()
    
    for i, track in enumerate(tracking_results):
        track_id = track['track_id']
        bbox = track['bbox']
        pose = track['pose']
        
        # Get color for this track
        color = TRACK_COLORS[track_id % len(TRACK_COLORS)]
        
        # Draw bounding box
        if torch.is_tensor(bbox):
            bbox = bbox.cpu().numpy()
        
        if isinstance(bbox, (list, tuple)):
            x1, y1, x2, y2 = int(bbox[0]), int(bbox[1]), int(bbox[2]), int(bbox[3])
        else:
            x1, y1, x2, y2 = bbox.astype(int)
        cv2.rectangle(img, (x1, y1), (x2, y2), color, 2)
        
        # Draw track ID
        if show_ids:
            cv2.putText(img, f'ID: {track_id}', (x1, y1 - 10), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        # Draw pose
        if show_uncertainty and 'pose_sigma' in track:
            img = draw_pose_with_uncertainty(img, pose, track['pose_sigma'], color)
        else:
            img = draw_pose_simple(img, pose, color)
    
    return img


def draw_pose_simple(image, pose, color=(0, 255, 0), thickness=2):
    """Draw pose without uncertainty visualization."""
    img = image.copy()
    
    # Convert to numpy if needed
    if torch.is_tensor(pose):
        pose = pose.cpu().numpy()
    
    # Draw skeleton connections
    for connection in SKELETON_CONNECTIONS:
        pt1_idx, pt2_idx = connection
        
        if pt1_idx < len(pose) and pt2_idx < len(pose):
            pt1 = tuple(pose[pt1_idx].astype(int))
            pt2 = tuple(pose[pt2_idx].astype(int))
            
            # Check if points are valid
            if (0 <= pt1[0] < img.shape[1] and 0 <= pt1[1] < img.shape[0] and
                0 <= pt2[0] < img.shape[1] and 0 <= pt2[1] < img.shape[0]):
                cv2.line(img, pt1, pt2, color, thickness)
    
    # Draw keypoints
    for point in pose:
        if isinstance(point, (list, tuple)):
            x, y = int(point[0]), int(point[1])
        else:
            x, y = point.astype(int)
        if 0 <= x < img.shape[1] and 0 <= y < img.shape[0]:
            cv2.circle(img, (x, y), 3, color, -1)
    
    return img


def visualize_uncertainty_distribution(pose_sigma, joint_names=None, save_path=None):
    """
    Visualize uncertainty distribution across joints.
    
    Args:
        pose_sigma: Uncertainty values (num_joints, 2)
        joint_names: Names of joints
        save_path: Path to save the plot
    """
    if torch.is_tensor(pose_sigma):
        pose_sigma = pose_sigma.cpu().numpy()
    
    if joint_names is None:
        joint_names = [
            "nose", "head_bottom", "head_top", "left_ear", "right_ear",
            "left_shoulder", "right_shoulder", "left_elbow", "right_elbow",
            "left_wrist", "right_wrist", "left_hip", "right_hip",
            "left_knee", "right_knee", "left_ankle", "right_ankle"
        ]
    
    # Compute average uncertainty per joint
    avg_uncertainty = np.mean(pose_sigma, axis=1)
    
    # Create bar plot
    plt.figure(figsize=(12, 6))
    bars = plt.bar(range(len(avg_uncertainty)), avg_uncertainty)
    
    # Color bars based on uncertainty level
    for i, bar in enumerate(bars):
        if avg_uncertainty[i] > 5.0:
            bar.set_color('red')
        elif avg_uncertainty[i] > 2.0:
            bar.set_color('orange')
        else:
            bar.set_color('green')
    
    plt.xlabel('Joint')
    plt.ylabel('Average Uncertainty')
    plt.title('Pose Uncertainty Distribution')
    plt.xticks(range(len(joint_names)), joint_names, rotation=45, ha='right')
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
    else:
        plt.show()


def create_tracking_video(frames, tracking_results_list, output_path, fps=30):
    """
    Create video with tracking results.
    
    Args:
        frames: List of frame images
        tracking_results_list: List of tracking results for each frame
        output_path: Output video path
        fps: Frames per second
    """
    if len(frames) == 0:
        return
    
    # Get video properties
    height, width = frames[0].shape[:2]
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    for frame, tracking_results in zip(frames, tracking_results_list):
        # Draw tracking results
        frame_with_tracks = draw_tracking_results(frame, tracking_results)
        
        # Add frame number
        cv2.putText(frame_with_tracks, f'Frame: {len(frames)}', (10, 30),
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
        
        out.write(frame_with_tracks)
    
    out.release()


def plot_tracking_metrics(metrics_history, save_path=None):
    """
    Plot tracking metrics over time.
    
    Args:
        metrics_history: Dictionary with metric values over time
        save_path: Path to save the plot
    """
    fig, axes = plt.subplots(2, 2, figsize=(12, 8))
    
    # MOTA
    if 'MOTA' in metrics_history:
        axes[0, 0].plot(metrics_history['MOTA'])
        axes[0, 0].set_title('MOTA (Multiple Object Tracking Accuracy)')
        axes[0, 0].set_ylabel('MOTA')
        axes[0, 0].grid(True)
    
    # MOTP
    if 'MOTP' in metrics_history:
        axes[0, 1].plot(metrics_history['MOTP'])
        axes[0, 1].set_title('MOTP (Multiple Object Tracking Precision)')
        axes[0, 1].set_ylabel('MOTP')
        axes[0, 1].grid(True)
    
    # Number of tracks
    if 'num_tracks' in metrics_history:
        axes[1, 0].plot(metrics_history['num_tracks'])
        axes[1, 0].set_title('Number of Active Tracks')
        axes[1, 0].set_ylabel('Count')
        axes[1, 0].grid(True)
    
    # ID switches
    if 'id_switches' in metrics_history:
        axes[1, 1].plot(metrics_history['id_switches'])
        axes[1, 1].set_title('ID Switches')
        axes[1, 1].set_ylabel('Count')
        axes[1, 1].grid(True)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path)
    else:
        plt.show()


def visualize_pose_comparison(image, gt_pose, pred_pose, pred_sigma=None):
    """
    Visualize comparison between ground truth and predicted poses.
    
    Args:
        image: Input image
        gt_pose: Ground truth pose
        pred_pose: Predicted pose
        pred_sigma: Predicted uncertainty (optional)
    """
    img = image.copy()
    
    # Draw ground truth in green
    img = draw_pose_simple(img, gt_pose, color=(0, 255, 0))
    
    # Draw prediction in red (with uncertainty if available)
    if pred_sigma is not None:
        img = draw_pose_with_uncertainty(img, pred_pose, pred_sigma, color=(0, 0, 255))
    else:
        img = draw_pose_simple(img, pred_pose, color=(0, 0, 255))
    
    # Add legend
    cv2.putText(img, 'Green: GT, Red: Pred', (10, 30),
               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
    
    return img


def visualize_pose_matching(image_path, pred_poses, gt_poses, joint_names, save_path, pred_scores=None):
    """
    시각화 함수: 예측 포즈와 GT 포즈를 같은 이미지에 그려서 비교
    
    Args:
        image_path: 원본 이미지 경로
        pred_poses: 예측 포즈 (num_persons, num_joints, 2)
        gt_poses: GT 포즈 (num_persons, num_joints, 2)
        joint_names: 관절 이름 리스트
        save_path: 저장할 이미지 경로
        pred_scores: 예측 스코어 (optional)
    """
    # 이미지 로드
    if isinstance(image_path, str) and os.path.exists(image_path):
        image = cv2.imread(image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    else:
        # 더미 이미지 생성
        image = np.zeros((480, 640, 3), dtype=np.uint8)
        image.fill(128)  # 회색 배경
    
    plt.figure(figsize=(15, 10))
    plt.imshow(image)
    plt.title('Pose Matching: Blue=GT, Red=Predicted')
    
    # GT 포즈 그리기 (파란색)
    if len(gt_poses) > 0:
        for person_idx, person_pose in enumerate(gt_poses):
            if len(person_pose) > 0:
                draw_pose_on_plot(person_pose, color='blue', alpha=0.7, 
                                person_id=f'GT_{person_idx}')
    
    # 예측 포즈 그리기 (빨간색)
    if len(pred_poses) > 0:
        for person_idx, person_pose in enumerate(pred_poses):
            if len(person_pose) > 0:
                score_text = ""
                if pred_scores is not None and person_idx < len(pred_scores):
                    score_text = f" (score: {pred_scores[person_idx]:.2f})"
                draw_pose_on_plot(person_pose, color='red', alpha=0.7, 
                                person_id=f'Pred_{person_idx}{score_text}')
    
    plt.legend()
    plt.axis('off')
    
    # 저장 디렉토리 생성
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    plt.savefig(save_path, bbox_inches='tight', dpi=150)
    plt.close()


def draw_pose_on_plot(pose, color='red', alpha=0.8, person_id=""):
    """
    matplotlib plot에 포즈를 그리는 함수
    
    Args:
        pose: (num_joints, 2) 포즈 좌표
        color: 색상
        alpha: 투명도
        person_id: 사람 식별자
    """
    if len(pose) == 0:
        return
        
    pose = np.array(pose)
    
    # 관절 그리기
    valid_joints = pose[:, 0] > 0  # x > 0인 관절만 그리기
    if np.any(valid_joints):
        plt.scatter(pose[valid_joints, 0], pose[valid_joints, 1], 
                   c=color, s=50, alpha=alpha, label=person_id if person_id else None)
    
    # 스켈레톤 연결 그리기
    for connection in SKELETON_CONNECTIONS:
        joint1_idx, joint2_idx = connection
        if (joint1_idx < len(pose) and joint2_idx < len(pose) and
            pose[joint1_idx, 0] > 0 and pose[joint1_idx, 1] > 0 and
            pose[joint2_idx, 0] > 0 and pose[joint2_idx, 1] > 0):
            
            x_coords = [pose[joint1_idx, 0], pose[joint2_idx, 0]]
            y_coords = [pose[joint1_idx, 1], pose[joint2_idx, 1]]
            plt.plot(x_coords, y_coords, color=color, alpha=alpha, linewidth=2)


def visualize_batch_predictions(images, predictions, targets, save_path):
    """
    Visualize batch predictions for debugging.
    
    Args:
        images: List of input images
        predictions: List of prediction dictionaries
        targets: List of target dictionaries
        save_path: Path to save visualization
    """
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        
        # Create figure
        fig, axes = plt.subplots(2, 2, figsize=(12, 8))
        axes = axes.flatten()
        
        num_images = min(len(images), 4)
        
        for i in range(num_images):
            ax = axes[i]
            
            # Convert image tensor to numpy
            if torch.is_tensor(images[i]):
                img = images[i].cpu().numpy()
                if img.shape[0] == 3:  # CHW format
                    img = np.transpose(img, (1, 2, 0))
                # Normalize to [0, 1]
                img = (img - img.min()) / (img.max() - img.min() + 1e-8)
            else:
                img = np.zeros((256, 192, 3))
            
            ax.imshow(img)
            ax.set_title(f'Image {i}')
            ax.axis('off')
            
            # Draw predictions if available
            if i < len(predictions) and predictions[i]:
                pred = predictions[i]
                if isinstance(pred, list) and len(pred) > 0:
                    pred = pred[0]  # Take first prediction
                
                if 'pose_mu' in pred:
                    pose = pred['pose_mu']
                    if torch.is_tensor(pose):
                        pose = pose.cpu().numpy()
                    
                    # Draw keypoints
                    if len(pose) > 0:
                        ax.scatter(pose[:, 0], pose[:, 1], c='red', s=20, alpha=0.8)
        
        # Hide unused subplots
        for i in range(num_images, 4):
            axes[i].axis('off')
        
        plt.tight_layout()
        
        # Create save directory
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
        
    except Exception as e:
        print(f"Visualization failed: {e}")
        # Create empty file to indicate attempt
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        with open(save_path, 'w') as f:
            f.write(f"Visualization failed: {e}")
