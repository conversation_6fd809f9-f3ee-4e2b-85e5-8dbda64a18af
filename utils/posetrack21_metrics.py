"""
PoseTrack21 evaluation metrics implementation
"""

import numpy as np
import torch
from collections import defaultdict
import json
import os


class PoseTrack21Evaluator:
    """
    PoseTrack21 evaluation following official evaluation protocol
    
    Metrics:
    - PCK (Percentage of Correct Keypoints) for pose estimation
    - MOTA (Multiple Object Tracking Accuracy) for tracking
    - MOTP (Multiple Object Tracking Precision) for tracking
    """
    
    def __init__(self, num_joints=17, pck_threshold=0.5, oks_threshold=0.5):
        self.num_joints = num_joints
        self.pck_threshold = pck_threshold
        self.oks_threshold = oks_threshold
        
        # Also add a more lenient PCK threshold for debugging
        self.pck_threshold_debug = 0.2  # 20% of head size instead of 50%
        
        # PoseTrack21 joint names
        self.joint_names = [
            "nose", "head_bottom", "head_top", "left_ear", "right_ear",
            "left_shoulder", "right_shoulder", "left_elbow", "right_elbow", 
            "left_wrist", "right_wrist", "left_hip", "right_hip",
            "left_knee", "right_knee", "left_ankle", "right_ankle"
        ]
        
        # OKS sigmas for each joint (from COCO)
        self.oks_sigmas = np.array([
            0.026, 0.025, 0.025, 0.035, 0.035, 0.079, 0.079, 0.072, 0.072,
            0.062, 0.062, 0.107, 0.107, 0.087, 0.087, 0.089, 0.089
        ])
        
        # Reset metrics
        self.reset()
    
    def reset(self):
        """Reset all metrics"""
        self.pose_results = []
        self.tracking_results = []
        self.gt_poses = []
        self.gt_tracks = []
        
    def add_batch(self, predictions, ground_truth, frame_info):
        """
        Add a batch of predictions and ground truth
        
        Args:
            predictions: Dict with 'poses', 'scores', 'track_ids'
            ground_truth: Dict with 'poses', 'track_ids', 'visibility'
            frame_info: Dict with 'vid_id', 'frame_id', 'image_size'
        """
        batch_size = len(frame_info)
        
        for i in range(batch_size):
            frame_data = frame_info[i]
            
            # Extract predictions for this frame
            if i < len(predictions['poses']):
                pred_poses = predictions['poses'][i]  # (N, 17, 2)
                pred_scores = predictions.get('scores', [1.0] * len(pred_poses))
                pred_track_ids = predictions.get('track_ids', list(range(len(pred_poses))))
            else:
                pred_poses = []
                pred_scores = []
                pred_track_ids = []
            
            # Extract ground truth for this frame
            if i < len(ground_truth['poses']):
                gt_poses = ground_truth['poses'][i]  # (M, 17, 2)
                gt_track_ids = ground_truth.get('track_ids', list(range(len(gt_poses))))
                gt_visibility = ground_truth.get('visibility', np.ones((len(gt_poses), 17)))
            else:
                gt_poses = []
                gt_track_ids = []
                gt_visibility = []
            
            # Store results
            self.pose_results.append({
                'vid_id': frame_data['vid_id'],
                'frame_id': frame_data['frame_id'],
                'pred_poses': pred_poses,
                'pred_scores': pred_scores,
                'pred_track_ids': pred_track_ids,
                'image_size': frame_data.get('image_size', (640, 480))
            })
            
            self.gt_poses.append({
                'vid_id': frame_data['vid_id'],
                'frame_id': frame_data['frame_id'],
                'gt_poses': gt_poses,
                'gt_track_ids': gt_track_ids,
                'gt_visibility': gt_visibility,
                'image_size': frame_data.get('image_size', (640, 480))
            })
    
    def compute_pck(self, debug=False):
        """Compute PCK (Percentage of Correct Keypoints)"""
        if not self.pose_results:
            return {'PCK': 0.0, 'PCK_per_joint': [0.0] * self.num_joints}

        total_keypoints = 0
        correct_keypoints = 0
        correct_per_joint = [0] * self.num_joints
        total_per_joint = [0] * self.num_joints

        frame_count = 0
        debug_info = []
        
        for pred_data, gt_data in zip(self.pose_results, self.gt_poses):
            if len(pred_data['pred_poses']) == 0 or len(gt_data['gt_poses']) == 0:
                continue

            pred_poses = np.array(pred_data['pred_poses'])
            gt_poses = np.array(gt_data['gt_poses'])
            
            if debug and frame_count < 2:  # Debug first 2 frames
                print(f"\nDEBUG Frame {frame_count}:")
                print(f"  Pred poses shape: {pred_poses.shape}")
                print(f"  GT poses shape: {gt_poses.shape}")
                print(f"  Pred pose sample (first person, first 3 joints): {pred_poses[0][:3] if len(pred_poses) > 0 else 'None'}")
                print(f"  GT pose sample (first person, first 3 joints): {gt_poses[0][:3] if len(gt_poses) > 0 else 'None'}")
            
            # Handle visibility array properly
            gt_visibility = gt_data['gt_visibility']
            if isinstance(gt_visibility, list):
                # Simplify - assume all joints are visible for now
                gt_visibility = np.ones((len(gt_poses), self.num_joints))
            else:
                gt_visibility = np.array(gt_visibility)
                if gt_visibility.ndim == 1:
                    gt_visibility = gt_visibility.reshape(1, -1)
                # Ensure correct shape
                if gt_visibility.shape[1] != self.num_joints:
                    new_vis = np.ones((gt_visibility.shape[0], self.num_joints))
                    min_joints = min(gt_visibility.shape[1], self.num_joints)
                    new_vis[:, :min_joints] = gt_visibility[:, :min_joints]
                    gt_visibility = new_vis

            # Match predictions to ground truth (simple nearest neighbor)
            matches = self._match_poses(pred_poses, gt_poses)
            frame_count += 1
            
            if debug and frame_count <= 2:
                print(f"  Found {len(matches)} matches: {matches}")
            
            for pred_idx, gt_idx in matches:
                if gt_idx >= len(gt_poses):
                    continue
                    
                pred_pose = pred_poses[pred_idx]
                gt_pose = gt_poses[gt_idx]
                visibility = gt_visibility[gt_idx] if gt_idx < len(gt_visibility) else np.ones(self.num_joints)
                
                # Compute head size for normalization
                head_size = self._compute_head_size(gt_pose)
                
                if debug and frame_count <= 2:
                    print(f"  Match {pred_idx}->{gt_idx}: head_size = {head_size:.2f}")
                    print(f"  PCK threshold = {self.pck_threshold} * {head_size:.2f} = {self.pck_threshold * head_size:.2f}")
                
                for joint_idx in range(self.num_joints):
                    # Handle different visibility array sizes
                    if joint_idx < len(visibility):
                        joint_visible = visibility[joint_idx]
                        if hasattr(joint_visible, '__len__'):
                            # If it's an array, check if any element is > 0
                            is_visible = np.any(joint_visible > 0)
                        else:
                            # If it's a scalar, check directly
                            is_visible = joint_visible > 0
                    else:
                        # If joint_idx is out of bounds, assume visible for testing
                        is_visible = True

                    if is_visible:  # Only evaluate visible joints
                        pred_joint = pred_pose[joint_idx]
                        gt_joint = gt_pose[joint_idx]

                        # Compute distance
                        distance = np.linalg.norm(pred_joint - gt_joint)
                        threshold = self.pck_threshold * head_size
                        
                        # Also try a more lenient threshold for debugging
                        lenient_threshold = self.pck_threshold_debug * head_size
                        
                        # Check if correct (within threshold * head_size)
                        is_correct = distance <= threshold
                        is_correct_lenient = distance <= lenient_threshold
                        
                        if debug and frame_count <= 2 and joint_idx < 3:  # Debug first 3 joints of first 2 frames
                            joint_name = self.joint_names[joint_idx] if joint_idx < len(self.joint_names) else f"joint_{joint_idx}"
                            print(f"    {joint_name}: pred={pred_joint}, gt={gt_joint}")
                            print(f"      dist={distance:.2f}, thresh={threshold:.2f}, lenient_thresh={lenient_threshold:.2f}")
                            print(f"      correct={is_correct}, correct_lenient={is_correct_lenient}")
                        
                        if is_correct:
                            correct_keypoints += 1
                            correct_per_joint[joint_idx] += 1

                        total_keypoints += 1
                        total_per_joint[joint_idx] += 1
        
        # Compute PCK
        pck = correct_keypoints / max(total_keypoints, 1)
        pck_per_joint = [
            correct_per_joint[i] / max(total_per_joint[i], 1)
            for i in range(self.num_joints)
        ]

        if debug:
            print(f"\nDEBUG PCK Summary:")
            print(f"  Total keypoints: {total_keypoints}")
            print(f"  Correct keypoints: {correct_keypoints}")
            print(f"  Overall PCK: {pck:.4f}")
            print(f"  Total per joint: {total_per_joint[:3]}")  # First 3 joints
            print(f"  Correct per joint: {correct_per_joint[:3]}")  # First 3 joints

        return {
            'PCK': pck,
            'PCK_per_joint': pck_per_joint,
            'joint_names': self.joint_names,
            'debug_info': debug_info if debug else []
        }
    
    def compute_pck_simple(self, pixel_threshold=10, debug=False):
        """Compute PCK with simple pixel threshold (no head size normalization)"""
        if not self.pose_results:
            return {'PCK_simple': 0.0, 'PCK_per_joint_simple': [0.0] * self.num_joints}

        total_keypoints = 0
        correct_keypoints = 0
        correct_per_joint = [0] * self.num_joints
        total_per_joint = [0] * self.num_joints

        frame_count = 0
        
        for pred_data, gt_data in zip(self.pose_results, self.gt_poses):
            if len(pred_data['pred_poses']) == 0 or len(gt_data['gt_poses']) == 0:
                continue

            pred_poses = np.array(pred_data['pred_poses'])
            gt_poses = np.array(gt_data['gt_poses'])
            
            if debug and frame_count < 2:
                print(f"\nDEBUG Simple PCK Frame {frame_count}:")
                print(f"  Using pixel threshold: {pixel_threshold}")
            
            # Match predictions to ground truth
            matches = self._match_poses(pred_poses, gt_poses)
            frame_count += 1
            
            for pred_idx, gt_idx in matches:
                if gt_idx >= len(gt_poses):
                    continue
                    
                pred_pose = pred_poses[pred_idx]
                gt_pose = gt_poses[gt_idx]
                
                for joint_idx in range(self.num_joints):
                    pred_joint = pred_pose[joint_idx]
                    gt_joint = gt_pose[joint_idx]

                    # Compute distance
                    distance = np.linalg.norm(pred_joint - gt_joint)
                    
                    # Simple pixel threshold
                    is_correct = distance <= pixel_threshold
                    
                    if debug and frame_count <= 2 and joint_idx < 3:
                        joint_name = self.joint_names[joint_idx] if joint_idx < len(self.joint_names) else f"joint_{joint_idx}"
                        print(f"    {joint_name}: dist={distance:.2f}, correct={is_correct}")
                    
                    if is_correct:
                        correct_keypoints += 1
                        correct_per_joint[joint_idx] += 1

                    total_keypoints += 1
                    total_per_joint[joint_idx] += 1
        
        # Compute PCK
        pck = correct_keypoints / max(total_keypoints, 1)
        pck_per_joint = [
            correct_per_joint[i] / max(total_per_joint[i], 1)
            for i in range(self.num_joints)
        ]

        if debug:
            print(f"\nDEBUG Simple PCK Summary (threshold={pixel_threshold}px):")
            print(f"  Total keypoints: {total_keypoints}")
            print(f"  Correct keypoints: {correct_keypoints}")
            print(f"  Simple PCK: {pck:.4f}")

        return {
            'PCK_simple': pck,
            'PCK_per_joint_simple': pck_per_joint
        }
    
    def compute_oks(self):
        """Compute OKS (Object Keypoint Similarity)"""
        if not self.pose_results:
            return {'mOKS': 0.0}
        
        total_oks = 0
        num_matches = 0
        
        for pred_data, gt_data in zip(self.pose_results, self.gt_poses):
            if len(pred_data['pred_poses']) == 0 or len(gt_data['gt_poses']) == 0:
                continue
            
            pred_poses = np.array(pred_data['pred_poses'])
            gt_poses = np.array(gt_data['gt_poses'])
            
            # Handle visibility array properly - simplified
            gt_visibility = gt_data['gt_visibility']
            if isinstance(gt_visibility, list):
                # Assume all joints are visible for now
                gt_visibility = np.ones((len(gt_poses), self.num_joints))
            else:
                gt_visibility = np.array(gt_visibility)
                if gt_visibility.ndim == 1:
                    gt_visibility = gt_visibility.reshape(1, -1)
                # Ensure correct shape
                if gt_visibility.shape[1] != self.num_joints:
                    new_vis = np.ones((gt_visibility.shape[0], self.num_joints))
                    min_joints = min(gt_visibility.shape[1], self.num_joints)
                    new_vis[:, :min_joints] = gt_visibility[:, :min_joints]
                    gt_visibility = new_vis
            
            # Match predictions to ground truth
            matches = self._match_poses(pred_poses, gt_poses)
            
            for pred_idx, gt_idx in matches:
                if gt_idx >= len(gt_poses):
                    continue
                    
                pred_pose = pred_poses[pred_idx]
                gt_pose = gt_poses[gt_idx]
                visibility = gt_visibility[gt_idx] if gt_idx < len(gt_visibility) else np.ones(self.num_joints)
                
                # Skip if pose contains NaN values
                if np.any(np.isnan(pred_pose)) or np.any(np.isnan(gt_pose)):
                    continue
                
                # Compute OKS
                oks = self._compute_oks(pred_pose, gt_pose, visibility)
                total_oks += oks
                num_matches += 1
        
        moks = total_oks / max(num_matches, 1)
        return {'mOKS': moks}
    
    def compute_tracking_metrics(self):
        """
        Compute tracking metrics (MOTA, MOTP, IDF1) using motmetrics
        """
        try:
            import motmetrics as mm
        except ImportError:
            print("Warning: motmetrics not available. Skipping tracking metrics.")
            return {
                'MOTA': 0.0,
                'MOTP': 0.0,
                'IDF1': 0.0,
                'num_misses': 0,
                'num_false_positives': 0,
                'num_switches': 0
            }
        
        # Group results by video sequence
        sequences = defaultdict(list)
        gt_sequences = defaultdict(list)
        
        for result in self.pose_results:
            vid_id = result['vid_id']
            sequences[vid_id].append(result)
            
        for gt in self.gt_poses:
            vid_id = gt['vid_id']
            gt_sequences[vid_id].append(gt)
        
        # Compute metrics for each sequence
        accumulators = []
        
        for vid_id in sequences.keys():
            if vid_id not in gt_sequences:
                continue
                
            acc = mm.MOTAccumulator(auto_id=True)
            
            # Sort by frame_id
            seq_results = sorted(sequences[vid_id], key=lambda x: x['frame_id'])
            seq_gt = sorted(gt_sequences[vid_id], key=lambda x: x['frame_id'])
            
            # Create frame-to-frame mapping
            gt_dict = {gt['frame_id']: gt for gt in seq_gt}
            
            for result in seq_results:
                frame_id = result['frame_id']
                
                if frame_id not in gt_dict:
                    continue
                
                gt_data = gt_dict[frame_id]
                
                # Get predictions and ground truth for this frame
                pred_poses = result['pred_poses']
                pred_track_ids = result['pred_track_ids']
                gt_poses_data = gt_data['gt_poses']
                gt_track_ids = gt_data['gt_track_ids']
                
                # Convert poses to bounding boxes for tracking evaluation
                pred_boxes = []
                gt_boxes = []
                
                if len(pred_poses) > 0:
                    pred_poses_np = np.array(pred_poses)
                    for i, pose in enumerate(pred_poses_np):
                        # Convert pose to bounding box [x1, y1, x2, y2]
                        valid_joints = pose[pose[:, 0] > 0]  # Filter valid joints
                        if len(valid_joints) > 0:
                            x1, y1 = valid_joints.min(axis=0)
                            x2, y2 = valid_joints.max(axis=0)
                            # Add some padding
                            padding = 0.1
                            w, h = x2 - x1, y2 - y1
                            x1 = max(0, x1 - w * padding)
                            y1 = max(0, y1 - h * padding)
                            x2 = x2 + w * padding
                            y2 = y2 + h * padding
                            pred_boxes.append([x1, y1, x2, y2])
                        else:
                            pred_boxes.append([0, 0, 10, 10])  # Dummy box
                
                if len(gt_poses_data) > 0:
                    gt_poses_np = np.array(gt_poses_data)
                    for i, pose in enumerate(gt_poses_np):
                        # Convert pose to bounding box
                        valid_joints = pose[pose[:, 0] > 0]  # Filter valid joints
                        if len(valid_joints) > 0:
                            x1, y1 = valid_joints.min(axis=0)
                            x2, y2 = valid_joints.max(axis=0)
                            # Add some padding
                            padding = 0.1
                            w, h = x2 - x1, y2 - y1
                            x1 = max(0, x1 - w * padding)
                            y1 = max(0, y1 - h * padding)
                            x2 = x2 + w * padding
                            y2 = y2 + h * padding
                            gt_boxes.append([x1, y1, x2, y2])
                        else:
                            gt_boxes.append([0, 0, 10, 10])  # Dummy box
                
                # Compute IoU distances between predictions and ground truth
                if len(pred_boxes) > 0 and len(gt_boxes) > 0:
                    pred_boxes = np.array(pred_boxes)
                    gt_boxes = np.array(gt_boxes)
                    
                    # Compute IoU matrix
                    distances = self._compute_iou_distance_matrix(pred_boxes, gt_boxes)
                    
                    # Convert high IoU to low distance (1 - IoU)
                    distances = 1.0 - distances
                    
                    # Update accumulator
                    acc.update(
                        gt_track_ids,
                        pred_track_ids,
                        distances
                    )
                else:
                    # No predictions or no ground truth
                    acc.update(
                        gt_track_ids if len(gt_boxes) > 0 else [],
                        pred_track_ids if len(pred_boxes) > 0 else [],
                        []
                    )
            
            accumulators.append(acc)
        
        # Compute metrics across all sequences
        if accumulators:
            mh = mm.metrics.create()
            summary = mh.compute_many(
                accumulators,
                metrics=['mota', 'motp', 'idf1', 'num_misses', 'num_false_positives', 'num_switches'],
                names=[f'seq_{i}' for i in range(len(accumulators))]
            )
            
            # Get overall metrics
            metrics = {
                'MOTA': summary['mota'].mean() if 'mota' in summary.columns else 0.0,
                'MOTP': summary['motp'].mean() if 'motp' in summary.columns else 0.0,
                'IDF1': summary['idf1'].mean() if 'idf1' in summary.columns else 0.0,
                'num_misses': summary['num_misses'].sum() if 'num_misses' in summary.columns else 0,
                'num_false_positives': summary['num_false_positives'].sum() if 'num_false_positives' in summary.columns else 0,
                'num_switches': summary['num_switches'].sum() if 'num_switches' in summary.columns else 0
            }
        else:
            metrics = {
                'MOTA': 0.0,
                'MOTP': 0.0,
                'IDF1': 0.0,
                'num_misses': 0,
                'num_false_positives': 0,
                'num_switches': 0
            }
        
        return metrics
    
    def _match_poses(self, pred_poses, gt_poses, distance_threshold=50.0):
        """
        Match predicted poses to ground truth poses using simple distance matching.
        
        Args:
            pred_poses: Array of predicted poses (N, num_joints, 2)
            gt_poses: Array of ground truth poses (M, num_joints, 2)
            distance_threshold: Maximum distance threshold for matching
            
        Returns:
            List of (pred_idx, gt_idx) tuples for matched poses
        """
        if len(pred_poses) == 0 or len(gt_poses) == 0:
            return []
        
        matches = []
        used_gt = set()
        
        # For each predicted pose, find the closest ground truth pose
        for pred_idx, pred_pose in enumerate(pred_poses):
            best_gt_idx = -1
            best_distance = float('inf')
            
            for gt_idx, gt_pose in enumerate(gt_poses):
                if gt_idx in used_gt:
                    continue
                
                # Compute average distance between keypoints
                distances = np.linalg.norm(pred_pose - gt_pose, axis=1)
                avg_distance = np.mean(distances)
                
                if avg_distance < best_distance and avg_distance < distance_threshold:
                    best_distance = avg_distance
                    best_gt_idx = gt_idx
            
            if best_gt_idx != -1:
                matches.append((pred_idx, best_gt_idx))
                used_gt.add(best_gt_idx)
        
        return matches

    def _compute_iou_distance_matrix(self, boxes1, boxes2):
        """
        Compute IoU distance matrix between two sets of boxes.
        
        Args:
            boxes1: (N, 4) array of boxes [x1, y1, x2, y2]
            boxes2: (M, 4) array of boxes [x1, y1, x2, y2]
            
        Returns:
            (N, M) array of IoU values
        """
        # Convert to torch tensors for easier computation
        boxes1 = torch.tensor(boxes1, dtype=torch.float32)
        boxes2 = torch.tensor(boxes2, dtype=torch.float32)
        
        # Compute IoU using torchvision
        from torchvision.ops import box_iou
        iou_matrix = box_iou(boxes1, boxes2)
        
        return iou_matrix.numpy()
    
    def evaluate(self, debug=False):
        """Compute all evaluation metrics"""
        pck_results = self.compute_pck(debug=debug)
        pck_simple_results = self.compute_pck_simple(pixel_threshold=10, debug=debug)
        pck_simple_lenient_results = self.compute_pck_simple(pixel_threshold=25, debug=debug)  # More lenient
        oks_results = self.compute_oks()
        tracking_results = self.compute_tracking_metrics()  # Compute tracking metrics
        
        results = {
            'PCK': pck_results['PCK'],
            'PCK_per_joint': pck_results['PCK_per_joint'],
            'PCK_simple_10px': pck_simple_results['PCK_simple'],
            'PCK_simple_25px': pck_simple_lenient_results['PCK_simple'],
            'mOKS': oks_results['mOKS'],
            'joint_names': self.joint_names,
            'MOTA': tracking_results['MOTA'],
            'MOTP': tracking_results['MOTP'],
            'IDF1': tracking_results['IDF1'],
            'num_misses': tracking_results['num_misses'],
            'num_false_positives': tracking_results['num_false_positives'],
            'num_switches': tracking_results['num_switches']
        }
        
        return results
    
    def print_results(self, results):
        """Print evaluation results in a nice format"""
        print("\n" + "="*60)
        print("PoseTrack21 Evaluation Results")
        print("="*60)
        
        print(f"Overall PCK (head-size normalized): {results['PCK']:.3f}")
        print(f"Simple PCK (10px threshold): {results.get('PCK_simple_10px', 0.0):.3f}")
        print(f"Simple PCK (25px threshold): {results.get('PCK_simple_25px', 0.0):.3f}")
        print(f"Mean OKS: {results['mOKS']:.3f}")
        print(f"MOTA: {results['MOTA']:.3f}")
        print(f"MOTP: {results['MOTP']:.3f}")
        print(f"IDF1: {results['IDF1']:.3f}")
        print(f"Num Misses: {results['num_misses']}")
        print(f"Num False Positives: {results['num_false_positives']}")
        print(f"Num Switches: {results['num_switches']}")
        
        print("\nPer-joint PCK (head-size normalized):")
        for i, (joint_name, pck) in enumerate(zip(results['joint_names'], results['PCK_per_joint'])):
            print(f"  {joint_name:15}: {pck:.3f}")
        
        print("="*60)
