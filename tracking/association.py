"""
Association modules for multi-person tracking with RLE uncertainty
"""

import torch
import numpy as np
from scipy.optimize import linear_sum_assignment
from scipy.spatial.distance import cdist


class BaseAssociation:
    """Base class for association algorithms."""
    
    def __init__(self, bbox_weight=0.3, pose_weight=0.7, max_distance=100.0):
        self.bbox_weight = bbox_weight
        self.pose_weight = pose_weight
        self.max_distance = max_distance
    
    def associate(self, tracks, detections):
        """
        Associate tracks with detections.
        
        Args:
            tracks: List of track states
            detections: List of detection dictionaries
            
        Returns:
            Tuple of (matches, unmatched_tracks, unmatched_detections)
        """
        raise NotImplementedError


class DistributionAwareAssociation(BaseAssociation):
    """
    Distribution-aware association using RLE uncertainties.
    
    This association method considers pose uncertainties when computing
    similarity between tracks and detections, making it more robust
    to uncertain or occluded keypoints.
    """
    
    def __init__(self, bbox_weight=0.3, pose_weight=0.7, max_distance=100.0, 
                 uncertainty_threshold=10.0, min_visible_joints=5):
        super().__init__(bbox_weight, pose_weight, max_distance)
        self.uncertainty_threshold = uncertainty_threshold
        self.min_visible_joints = min_visible_joints
    
    def associate(self, tracks, detections):
        """
        Perform distribution-aware association.
        
        Args:
            tracks: List of track states with pose means and covariances
            detections: List of detections with pose_mu and pose_sigma
            
        Returns:
            Tuple of (matches, unmatched_tracks, unmatched_detections)
        """
        if len(tracks) == 0:
            return [], [], list(range(len(detections)))
        
        if len(detections) == 0:
            return [], list(range(len(tracks))), []
        
        # Compute cost matrix
        cost_matrix = self._compute_cost_matrix(tracks, detections)
        
        # Apply Hungarian algorithm
        track_indices, det_indices = linear_sum_assignment(cost_matrix)
        
        # Filter matches based on maximum distance
        matches = []
        unmatched_tracks = list(range(len(tracks)))
        unmatched_detections = list(range(len(detections)))
        
        for t_idx, d_idx in zip(track_indices, det_indices):
            if cost_matrix[t_idx, d_idx] <= self.max_distance:
                matches.append((t_idx, d_idx))
                unmatched_tracks.remove(t_idx)
                unmatched_detections.remove(d_idx)
        
        return matches, unmatched_tracks, unmatched_detections
    
    def _compute_cost_matrix(self, tracks, detections):
        """
        Compute cost matrix between tracks and detections using RLE uncertainties.
        
        Args:
            tracks: List of track states
            detections: List of detections
            
        Returns:
            Cost matrix (num_tracks, num_detections)
        """
        num_tracks = len(tracks)
        num_detections = len(detections)
        cost_matrix = np.full((num_tracks, num_detections), self.max_distance + 1)
        
        for t_idx, track in enumerate(tracks):
            for d_idx, detection in enumerate(detections):
                # Compute bounding box distance
                bbox_cost = self._compute_bbox_distance(
                    track.get('bbox', [0, 0, 0, 0]), 
                    detection['bbox']
                )
                
                # Compute pose distance with uncertainty weighting
                pose_cost = self._compute_pose_distance_with_uncertainty(
                    track, detection
                )
                
                # Combined cost
                total_cost = (self.bbox_weight * bbox_cost + 
                             self.pose_weight * pose_cost)
                
                cost_matrix[t_idx, d_idx] = total_cost
        
        return cost_matrix
    
    def _compute_bbox_distance(self, bbox1, bbox2):
        """Compute bounding box distance."""
        if len(bbox1) != 4 or len(bbox2) != 4:
            return self.max_distance
        
        # Center points
        center1 = np.array([(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2])
        center2 = np.array([(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2])
        
        return np.linalg.norm(center1 - center2)
    
    def _compute_pose_distance_with_uncertainty(self, track, detection):
        """
        Compute pose distance considering RLE uncertainties.
        
        Args:
            track: Track state with pose information
            detection: Detection with pose_mu and pose_sigma
            
        Returns:
            Weighted pose distance
        """
        # Get track pose (predicted or last observed)
        track_pose = track.get('pose_mu')
        if track_pose is None:
            return self.max_distance
        
        det_pose = detection['pose_mu']
        det_sigma = detection['pose_sigma']
        
        if det_pose is None or det_sigma is None:
            return self.max_distance
        
        # Convert to numpy arrays
        if isinstance(track_pose, torch.Tensor):
            track_pose = track_pose.cpu().numpy()
        if isinstance(det_pose, torch.Tensor):
            det_pose = det_pose.cpu().numpy()
        if isinstance(det_sigma, torch.Tensor):
            det_sigma = det_sigma.cpu().numpy()
        
        # Compute weighted distance based on uncertainties
        distances = []
        weights = []
        
        num_joints = min(len(track_pose), len(det_pose))
        
        for j in range(num_joints):
            # Joint distance
            joint_dist = np.linalg.norm(track_pose[j] - det_pose[j])
            
            # Uncertainty weight (lower uncertainty = higher weight)
            uncertainty = np.mean(det_sigma[j])
            if uncertainty < self.uncertainty_threshold:
                weight = 1.0 / (1.0 + uncertainty)
            else:
                weight = 0.1  # Low weight for very uncertain joints
            
            distances.append(joint_dist)
            weights.append(weight)
        
        # Check minimum visible joints
        reliable_joints = sum(w > 0.5 for w in weights)
        if reliable_joints < self.min_visible_joints:
            return self.max_distance
        
        # Weighted average distance
        if sum(weights) > 0:
            weighted_distance = sum(d * w for d, w in zip(distances, weights)) / sum(weights)
        else:
            weighted_distance = self.max_distance
        
        return weighted_distance


class ConfidenceBasedAssociation(BaseAssociation):
    """Simple confidence-based association for comparison."""
    
    def __init__(self, bbox_weight=0.3, pose_weight=0.7, max_distance=100.0, 
                 confidence_threshold=0.5):
        super().__init__(bbox_weight, pose_weight, max_distance)
        self.confidence_threshold = confidence_threshold
    
    def associate(self, tracks, detections):
        """Perform confidence-based association."""
        if len(tracks) == 0:
            return [], [], list(range(len(detections)))
        
        if len(detections) == 0:
            return [], list(range(len(tracks))), []
        
        # Filter detections by confidence
        valid_detections = []
        valid_indices = []
        for i, det in enumerate(detections):
            if det.get('score', 1.0) >= self.confidence_threshold:
                valid_detections.append(det)
                valid_indices.append(i)
        
        if len(valid_detections) == 0:
            return [], list(range(len(tracks))), list(range(len(detections)))
        
        # Compute simple cost matrix
        cost_matrix = np.full((len(tracks), len(valid_detections)), self.max_distance + 1)
        
        for t_idx, track in enumerate(tracks):
            for d_idx, detection in enumerate(valid_detections):
                bbox_cost = self._compute_bbox_distance(
                    track.get('bbox', [0, 0, 0, 0]), 
                    detection['bbox']
                )
                pose_cost = self._compute_simple_pose_distance(track, detection)
                
                total_cost = self.bbox_weight * bbox_cost + self.pose_weight * pose_cost
                cost_matrix[t_idx, d_idx] = total_cost
        
        # Hungarian assignment
        track_indices, det_indices = linear_sum_assignment(cost_matrix)
        
        # Filter matches and prepare results
        matches = []
        unmatched_tracks = list(range(len(tracks)))
        unmatched_detections = list(range(len(detections)))
        
        for t_idx, d_idx in zip(track_indices, det_indices):
            if cost_matrix[t_idx, d_idx] <= self.max_distance:
                matches.append((t_idx, valid_indices[d_idx]))
                unmatched_tracks.remove(t_idx)
                unmatched_detections.remove(valid_indices[d_idx])
        
        return matches, unmatched_tracks, unmatched_detections
    
    def _compute_bbox_distance(self, bbox1, bbox2):
        """Compute bounding box distance."""
        if len(bbox1) != 4 or len(bbox2) != 4:
            return self.max_distance
        
        center1 = np.array([(bbox1[0] + bbox1[2]) / 2, (bbox1[1] + bbox1[3]) / 2])
        center2 = np.array([(bbox2[0] + bbox2[2]) / 2, (bbox2[1] + bbox2[3]) / 2])
        
        return np.linalg.norm(center1 - center2)
    
    def _compute_simple_pose_distance(self, track, detection):
        """Compute simple pose distance without uncertainty weighting."""
        track_pose = track.get('pose_mu')
        det_pose = detection['pose_mu']
        
        if track_pose is None or det_pose is None:
            return self.max_distance
        
        # Convert to numpy arrays
        if isinstance(track_pose, torch.Tensor):
            track_pose = track_pose.cpu().numpy()
        if isinstance(det_pose, torch.Tensor):
            det_pose = det_pose.cpu().numpy()
        
        # Simple euclidean distance between all joints
        distances = []
        num_joints = min(len(track_pose), len(det_pose))
        
        for j in range(num_joints):
            joint_dist = np.linalg.norm(track_pose[j] - det_pose[j])
            distances.append(joint_dist)
        
        return np.mean(distances) if distances else self.max_distance
