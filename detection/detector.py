"""
Multi-person detection module for MPPET-RLE
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.ops as ops
from torchvision.models.detection import fasterrcnn_resnet50_fpn
from torchvision.models.detection.faster_rcnn import FastRCNNPredictor

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from builder import DETECTOR


@DETECTOR.register_module
class FasterRCNNDetector(nn.Module):
    """Faster R-CNN based person detector."""
    
    def __init__(self, pretrained=True, num_classes=2, score_threshold=0.5, nms_threshold=0.5):
        """
        Initialize Faster R-CNN detector.
        
        Args:
            pretrained: Whether to use pretrained weights
            num_classes: Number of classes (background + person)
            score_threshold: Score threshold for filtering detections
            nms_threshold: NMS threshold for filtering detections
        """
        super(FasterRCNNDetector, self).__init__()
        
        self.score_threshold = score_threshold
        self.nms_threshold = nms_threshold
        
        # Load pretrained Faster R-CNN
        self.model = fasterrcnn_resnet50_fpn(pretrained=pretrained)
        
        # Replace classifier head for person detection (background + person)
        in_features = self.model.roi_heads.box_predictor.cls_score.in_features
        self.model.roi_heads.box_predictor = FastRCNNPredictor(in_features, num_classes)
        
    def forward(self, images, targets=None):
        """
        Forward pass of the detector.
        
        Args:
            images: List of images or tensor of shape (B, C, H, W)
            targets: List of target dictionaries for training
            
        Returns:
            If training: losses dict
            If inference: list of detection results
        """
        if self.training and targets is not None:
            # Training mode - return losses
            return self.model(images, targets)
        else:
            # Inference mode - return detections
            self.model.eval()
            with torch.no_grad():
                predictions = self.model(images)
            
            # Filter predictions
            filtered_predictions = []
            for pred in predictions:
                # Filter by score threshold
                keep = pred['scores'] > self.score_threshold
                
                # Filter person class only (class 1)
                person_mask = pred['labels'] == 1
                keep = keep & person_mask
                
                if keep.sum() > 0:
                    filtered_pred = {
                        'boxes': pred['boxes'][keep],
                        'scores': pred['scores'][keep],
                        'labels': pred['labels'][keep]
                    }
                    
                    # Apply NMS
                    keep_nms = ops.nms(filtered_pred['boxes'], filtered_pred['scores'], self.nms_threshold)
                    
                    final_pred = {
                        'boxes': filtered_pred['boxes'][keep_nms],
                        'scores': filtered_pred['scores'][keep_nms],
                        'labels': filtered_pred['labels'][keep_nms]
                    }
                else:
                    final_pred = {
                        'boxes': torch.empty((0, 4), device=pred['boxes'].device),
                        'scores': torch.empty((0,), device=pred['scores'].device),
                        'labels': torch.empty((0,), device=pred['labels'].device, dtype=torch.long)
                    }
                
                filtered_predictions.append(final_pred)
            
            return filtered_predictions
    
    def extract_features(self, images):
        """Extract backbone features for pose estimation."""
        self.model.eval()
        with torch.no_grad():
            # Get backbone features
            features = self.model.backbone(images)
        return features


@DETECTOR.register_module  
class YOLODetector(nn.Module):
    """YOLO-based person detector (simplified implementation)."""
    
    def __init__(self, backbone='resnet50', num_classes=1, score_threshold=0.5, nms_threshold=0.5):
        """
        Initialize YOLO detector.
        
        Args:
            backbone: Backbone network
            num_classes: Number of classes (person only)
            score_threshold: Score threshold for filtering detections
            nms_threshold: NMS threshold for filtering detections
        """
        super(YOLODetector, self).__init__()
        
        self.score_threshold = score_threshold
        self.nms_threshold = nms_threshold
        self.num_classes = num_classes
        
        # Simplified YOLO head - in practice, use a proper YOLO implementation
        if backbone == 'resnet50':
            import torchvision.models as models
            self.backbone = models.resnet50(pretrained=True)
            self.backbone = nn.Sequential(*list(self.backbone.children())[:-2])  # Remove avgpool and fc
            feature_dim = 2048
        else:
            raise NotImplementedError(f"Backbone {backbone} not implemented")
        
        # YOLO detection head
        self.detection_head = nn.Sequential(
            nn.Conv2d(feature_dim, 512, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(512, 256, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(256, (5 + num_classes) * 3, 1)  # 3 anchors, 5 = (x, y, w, h, conf)
        )
        
    def forward(self, images, targets=None):
        """Forward pass of YOLO detector."""
        batch_size = images.shape[0]
        
        # Extract features
        features = self.backbone(images)
        
        # Detection head
        detections = self.detection_head(features)
        
        if self.training and targets is not None:
            # Training mode - compute losses
            # This is a simplified implementation
            # In practice, implement proper YOLO loss
            return {'loss_detection': torch.tensor(0.0, device=images.device, requires_grad=True)}
        else:
            # Inference mode - decode predictions
            # This is a simplified implementation
            # In practice, implement proper YOLO decoding
            predictions = []
            for i in range(batch_size):
                pred = {
                    'boxes': torch.empty((0, 4), device=images.device),
                    'scores': torch.empty((0,), device=images.device),
                    'labels': torch.empty((0,), device=images.device, dtype=torch.long)
                }
                predictions.append(pred)
            
            return predictions
    
    def extract_features(self, images):
        """Extract backbone features for pose estimation."""
        return self.backbone(images)


@DETECTOR.register_module
class YOLOv5Detector(nn.Module):
    """YOLOv5-based lightweight person detector for faster inference."""
    
    def __init__(self, model_name='yolov5s', score_threshold=0.5, nms_threshold=0.5, device='cpu'):
        """
        Initialize YOLOv5 detector.
        
        Args:
            model_name: YOLOv5 model variant ('yolov5n', 'yolov5s', 'yolov5m', etc.)
            score_threshold: Score threshold for filtering detections
            nms_threshold: NMS threshold for filtering detections
            device: Device to run model on
        """
        super(YOLOv5Detector, self).__init__()
        
        self.score_threshold = score_threshold
        self.nms_threshold = nms_threshold
        self.device = device
        
        try:
            # Try torch hub first as it's more reliable for downloading pretrained models
            self.model = torch.hub.load('ultralytics/yolov5', model_name, pretrained=True)
            self.model.to(device)
            print(f"Successfully loaded YOLOv5 model: {model_name}")
        except Exception as e:
            print(f"Warning: Could not load YOLOv5 model via torch hub: {e}")
            try:
                import ultralytics
                # Load YOLOv5 model from ultralytics - ensure .pt extension
                model_path = f'{model_name}.pt' if not model_name.endswith('.pt') else model_name
                self.model = ultralytics.YOLO(model_path)
                self.model.to(device)
                print(f"Successfully loaded ultralytics YOLO model: {model_path}")
            except Exception as e2:
                print(f"Warning: Could not load ultralytics YOLO model: {e2}")
                print("Falling back to simple detector")
                # Fallback to a simple CNN-based detector
                self._create_simple_detector()
        
        # Set to eval mode if model exists
        if hasattr(self, 'model') and self.model is not None:
            self.model.eval()
        
    def _create_simple_detector(self):
        """Create a simple CNN-based detector as fallback."""
        import torchvision.models as models
        backbone = models.resnet18(pretrained=True)
        self.backbone = nn.Sequential(*list(backbone.children())[:-2])
        
        # Simple detection head
        self.detection_head = nn.Sequential(
            nn.AdaptiveAvgPool2d((7, 7)),
            nn.Flatten(),
            nn.Linear(512 * 7 * 7, 1024),
            nn.ReLU(),
            nn.Linear(1024, 5)  # [x1, y1, x2, y2, score]
        )
        
        # Set self.model to None to indicate we're using simple detector
        self.model = None
        
    def forward(self, images, targets=None):
        """
        Forward pass of YOLOv5 detector.
        
        Args:
            images: List of images or tensor of shape (B, C, H, W)
            targets: List of target dictionaries for training (optional)
            
        Returns:
            If training mode: loss dictionary
            If eval mode: list of detection results
        """
        if self.training and targets is not None:
            # Training mode - return dummy losses for compatibility
            device = images[0].device if isinstance(images, list) else images.device
            return {
                'loss_classifier': torch.tensor(0.0, device=device, requires_grad=True),
                'loss_box_reg': torch.tensor(0.0, device=device, requires_grad=True),
                'loss_objectness': torch.tensor(0.0, device=device, requires_grad=True),
                'loss_rpn_box_reg': torch.tensor(0.0, device=device, requires_grad=True)
            }
        else:
            # Inference mode
            return self._inference(images)
    
    def _inference(self, images):
        """Run inference on images."""
        if isinstance(images, torch.Tensor):
            # Convert tensor to list of PIL Images or numpy arrays
            images_list = []
            for i in range(images.shape[0]):
                img_tensor = images[i]  # (C, H, W)
                # Convert to numpy (H, W, C) and scale to 0-255
                img_np = img_tensor.permute(1, 2, 0).cpu().numpy()
                if img_np.max() <= 1.0:
                    img_np = (img_np * 255).astype('uint8')
                images_list.append(img_np)
            images = images_list
        
        results = []
        
        try:
            # Use YOLOv5 model
            with torch.no_grad():
                if hasattr(self.model, 'predict'):
                    # Ultralytics YOLO API
                    yolo_results = self.model.predict(images, verbose=False)
                    for result in yolo_results:
                        boxes = []
                        scores = []
                        labels = []
                        
                        if result.boxes is not None:
                            for box in result.boxes:
                                # Filter for person class (class 0 in COCO)
                                if box.cls == 0:  # person class
                                    conf = float(box.conf)
                                    if conf >= self.score_threshold:
                                        # Convert to [x1, y1, x2, y2] format
                                        bbox = box.xyxy[0].cpu().numpy()
                                        boxes.append(bbox)
                                        scores.append(conf)
                                        labels.append(1)  # person label
                        
                        result_dict = {
                            'boxes': torch.tensor(boxes, dtype=torch.float32),
                            'scores': torch.tensor(scores, dtype=torch.float32),
                            'labels': torch.tensor(labels, dtype=torch.long)
                        }
                        results.append(result_dict)
                else:
                    # Torch hub YOLO API
                    yolo_results = self.model(images)
                    for i in range(len(images)):
                        detections = yolo_results.pandas().xyxy[i]
                        
                        # Filter for person class and confidence
                        person_detections = detections[
                            (detections['class'] == 0) & 
                            (detections['confidence'] >= self.score_threshold)
                        ]
                        
                        boxes = []
                        scores = []
                        labels = []
                        
                        for _, detection in person_detections.iterrows():
                            boxes.append([detection['xmin'], detection['ymin'], 
                                        detection['xmax'], detection['ymax']])
                            scores.append(detection['confidence'])
                            labels.append(1)  # person label
                        
                        result_dict = {
                            'boxes': torch.tensor(boxes, dtype=torch.float32),
                            'scores': torch.tensor(scores, dtype=torch.float32),
                            'labels': torch.tensor(labels, dtype=torch.long)
                        }
                        results.append(result_dict)
                        
        except Exception as e:
            print(f"YOLOv5 inference failed: {e}, using fallback")
            # Fallback to simple detector
            results = self._simple_inference(images)
        
        return results
    
    def _simple_inference(self, images):
        """Fallback simple inference."""
        results = []
        
        if isinstance(images, list):
            batch_size = len(images)
            # Convert to tensor for simple processing
            device = next(self.parameters()).device
            dummy_tensor = torch.zeros((batch_size, 3, 224, 224), device=device)
        else:
            batch_size = images.shape[0]
            dummy_tensor = images
        
        # Return dummy detections for fallback
        for i in range(batch_size):
            result_dict = {
                'boxes': torch.empty((0, 4), dtype=torch.float32),
                'scores': torch.empty((0,), dtype=torch.float32),
                'labels': torch.empty((0,), dtype=torch.long)
            }
            results.append(result_dict)
        
        return results


def build_detector(cfg):
    """Build detector from config."""
    detector_type = cfg.get('type', 'FasterRCNNDetector')
    
    if detector_type == 'FasterRCNNDetector':
        return FasterRCNNDetector(
            pretrained=cfg.get('pretrained', True),
            num_classes=cfg.get('num_classes', 2),
            score_threshold=cfg.get('score_threshold', 0.5),
            nms_threshold=cfg.get('nms_threshold', 0.5)
        )
    elif detector_type == 'YOLODetector':
        return YOLODetector(
            backbone=cfg.get('backbone', 'resnet50'),
            num_classes=cfg.get('num_classes', 1),
            score_threshold=cfg.get('score_threshold', 0.5),
            nms_threshold=cfg.get('nms_threshold', 0.5)
        )
    elif detector_type == 'YOLOv5Detector':
        return YOLOv5Detector(
            model_name=cfg.get('model_name', 'yolov5s'),
            score_threshold=cfg.get('score_threshold', 0.5),
            nms_threshold=cfg.get('nms_threshold', 0.5),
            device=cfg.get('device', 'cpu')
        )
    else:
        raise ValueError(f"Unknown detector type: {detector_type}")
