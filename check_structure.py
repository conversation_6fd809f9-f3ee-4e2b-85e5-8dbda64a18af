"""
MPPET-RLE Repository Structure Verification
"""

import os
import sys

def check_file_exists(filepath, description=""):
    """Check if a file exists and print status."""
    if os.path.exists(filepath):
        print(f"✓ {filepath} {description}")
        return True
    else:
        print(f"✗ {filepath} {description} - MISSING")
        return <PERSON>alse

def check_directory_structure():
    """Check the complete directory structure."""
    print("=== MPPET-RLE Repository Structure Check ===\n")
    
    # Core directories
    print("📁 Core Directories:")
    dirs = [
        "models/",
        "detection/", 
        "tracking/",
        "datasets/",
        "utils/",
        "configs/",
        "layers/",
        "checkpoints_posetrack21/",
        "logs_posetrack21/"
    ]
    
    for dir_path in dirs:
        check_file_exists(dir_path, "(directory)")
    
    print("\n📋 Core Model Files:")
    model_files = [
        "models/__init__.py",
        "models/mppet_rle.py",
        "models/pose_estimator.py", 
        "models/losses.py"
    ]
    
    for file_path in model_files:
        check_file_exists(file_path)
    
    print("\n🔧 Configuration Files:")
    config_files = [
        "configs/__init__.py",
        "configs/base_config.py",
        "configs/posetrack21_config.py"
    ]
    
    for file_path in config_files:
        check_file_exists(file_path)
    
    print("\n🎯 Training & Testing Scripts:")
    script_files = [
        "train_posetrack21.py",
        "test_posetrack21.py"
    ]
    
    for file_path in script_files:
        check_file_exists(file_path)
    
    print("\n📚 Support Files:")
    support_files = [
        "requirements.txt",
        "README.md",
        "setup.sh",
        "builder.py"
    ]
    
    for file_path in support_files:
        check_file_exists(file_path)
    
    print("\n🔍 Detection & Tracking:")
    detection_tracking_files = [
        "detection/__init__.py",
        "detection/detector.py",
        "tracking/__init__.py", 
        "tracking/tracker.py",
        "tracking/association.py",
        "tracking/motion_model.py",
        "tracking/track_manager.py"
    ]
    
    for file_path in detection_tracking_files:
        check_file_exists(file_path)
    
    print("\n🛠️ Utilities:")
    util_files = [
        "utils/__init__.py",
        "utils/metrics.py",
        "utils/posetrack21_metrics.py",
        "utils/visualization.py"
    ]
    
    for file_path in util_files:
        check_file_exists(file_path)
    
    print("\n🧮 Neural Network Layers:")
    layer_files = [
        "layers/__init__.py",
        "layers/Resnet.py", 
        "layers/real_nvp.py"
    ]
    
    for file_path in layer_files:
        check_file_exists(file_path)
    
    print("\n=== Structure Check Complete ===")
    print("\nRepository is organized as follows:")
    print("✓ Models separated into models/ package")
    print("✓ Configuration management in configs/")
    print("✓ Clear separation of detection, tracking, and pose estimation")
    print("✓ Comprehensive utilities and metrics")
    print("✓ PoseTrack21-specific training and testing scripts")
    print("✓ Proper package structure with __init__.py files")

if __name__ == "__main__":
    check_directory_structure()
